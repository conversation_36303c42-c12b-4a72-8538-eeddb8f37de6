export default {
  title: 'Title',
  ril: 'Read Later',
  back: 'Back',
  close: 'Close',
  cancel: 'Cancel',
  confirm: 'Confirm',
  confirm_done: 'Done',
  open: 'Open',
  create: 'Create',
  loadmore: 'Load More',
  refresh: 'refresh',

  export: 'Export',

  import: 'Import',
  error_msg: 'Something went wrong!',

  invite_code: 'Invite Code',
  invite_friends: 'Invite Friends',
  invite_friends_desc: 'reward AI tokens',
  or_invite_friend_rewards: 'or get rewards through',
  copy_intruction: 'Copy following text and send to friends:',
  homepage: 'Home page',
  feedback: 'Feedback',
  reply: 'Reply',
  notice: 'Notice',
  upgrade_now: 'Update now',
  upgrade_plan: 'Upgrade Now',
  reload: 'Reload',

  slides: 'Slides',

  share: 'Share',
  edit: 'Edit',

  open_to_public: 'Open to Public',
  everyone_at_org: 'Everyone at Space',
  everyone_at_group: 'Everyone at Group',

  copy_link: 'Copy Link',
  duplicate: 'Duplicate',
  copied: 'Copied to clipboard',
  copied_also: 'The content has also been copied to the clipboard',
  copied_to_share:
    'The shared content has been copied, and you can paste it into the sharing area',
  copy_failed: 'Failed to copy',

  slides_ai_converter: 'Make Slide with AI',

  ril: 'Read It Later',
  settings: 'Settings',
  quick_settings: 'Quick Settings',
  settings_account: 'Account Settings',
  settings_workspace: 'Workspace Settings',

  my_prompt: 'My prompt',
  assistant_reader: 'Reading assistant',
  assistant_writer: 'Writing assistant',
  assistant_with_page_context: 'AI Assistant with access to page content',
  assistant_without_page_context: 'AI Assistant',
  ask_ai: 'More AI actions',
  ask_ai_about_selected: 'As given text',
  ask_ai_about_selected_tooltip: 'Ask AI with selected text as context',
  ask_ai_about_given_text_hint:
    'Please enter or paste the text to be processed',
  selected_text: 'Selected text',
  selected_whole_page: 'Content selected',
  selected_subtitles: 'Subtitles selected',
  selected_email: 'Received mail content',
  injected_ai_trigger_placeholder:
    'Type {trigger} to show FunBlocks writing assistant',
  settings_ai_trigger: 'Writing assistant command',
  settings_ai_trigger_desc: `In any web page input box or editor, you can type {trigger} to invoke writing assistant`,
  settings_ai_trigger_none_desc:
    "After setting the command prefix, you can invoke the AI assistant by entering the command prefix anywhere in the page's input box or editor",
  settings_ai_trigger_conflict_warn: `'/' is a convenient input command, but if you frequently use editing software like Notion that uses '/' as a command prefix, it is recommended to choose '\\' or '\\\\'`,
  balloon_auto_hide: `Toolbar auto hide after 5 seconds`,
  balloon_copy_enabled: `Enable 'Copy' button in contextual toolbar`,
  balloon_auto_display: 'Auto Display',
  balloon_auto_display_desc: 'Display toolbar when you select any text',
  balloon_shortcut_display: 'Display on shortcut',
  balloon_shortcut_select_text: 'When select any text',
  balloon_shortcut_plus_hotkey: '+ {hotkey} to display toolbar',

  settings_my_account_menu: 'My Account',
  settings_my_account_title: 'My Account',
  settings_language_menu: 'Language',
  settings_language_title: 'Language Setting',
  settings_language_desc: 'User Interface Language',
  settings_ai_respond: 'AI Default Response Language',
  settings_ai_respond_desc:
    'The AI default response language setting primarily applies to AI reading assistant functions, such as "Explain" and "Translate"',
  set_lang_as_context: 'Context-adaptive language',
  set_ai_model: 'Set LLM model',
  set_default_ai_model: 'Set default LLM model',
  private_llm_key_list: 'Private LLM API Key List',
  self_owned_ai_api: 'Private LLM API Keys',
  add_private_llm_key: 'Add Private LLM API Key',
  get_models_test_token: 'Get Models/Test Key',
  token_valid: 'Key and Endpoint valid',
  token_invalid: 'Key or Endpoint invalid',
  settings_general: 'General',
  settings_api_menu: 'LLM Service Provider',
  settings_api_title: 'Large language model(LLM) API Settings',
  settings_api_enable_api: 'Enable LLM API access',
  settings_api_llms: 'Supported LLM service providers',
  settings_api_choose_model: 'Choose a model',
  settings_api_proxy: 'API endpoint(or proxy)',
  settings_api_desc:
    '### You have selected {provider} as the AI service provider:\n\n1. FunBlocks AI will directly access {provider} with the API Key you provided, without being limited by the free quota of FunBlocks AI service. \n2. The API Key is saved locally and will not be uploaded to the FunBlocks server, so there is no need to worry about being stolen.',
  openai_compatible:
    '### When using an LLM service compatible with the OpenAI interface, simply follow these steps:\n1. Enter the interface address in the "Endpoint" field;\n2. Choose the "Other" from "models" and provide the model name.',
  settings_funblocks_desc:
    'After registering a FunBlock account, you will receive a trial of 40 queries to use the AI assistant and enjoy 20 free AI assistant queries every day. Additionally, you have the option to upgrade to the FunBlock AI membership plan for unlimited access to the AI assistant.',
  llm_api_token_guide: 'You can login to {aiProvider} to get API Key',
  invalid_api_settings:
    'Please connect to a FunBlocks account, or choose a third-party AI LLM service provider in the settings page.',
  confirm_api_set: 'Is the setup complete?',
  has_ai_api: 'Has LLM API key?',
  settings_prompts_menu: 'Prompts and Apps',
  settings_prompts_title: 'AI Assistant Apps',
  settings_side_bar: 'Side Bar',
  settings_side_bar_desc:
    'Once enabled, the Sidebar will stay on right/left side of the browser. It provides quick access to FunBlocks AI assistants.',
  settings_side_bar_align: 'Show Sidebar at',
  left_side: 'Left side of browser',
  right_side: 'Right side of browser',
  settings_quick_compose: 'Writing Assistant',
  settings_quick_compose_desc: `After enabling the "Writing Assistant," when input box or editor gains focus, "Magic" button will appear on the right side. Clicking it will summon the "AI Writing Assistant."`,
  settings_quick_action: 'Contextual Toolbar',
  settings_quick_action_desc: `After enabling the "Quick Action," when you select any text with the mouse in the page or editor, the "Quick Action" float button will appear below the mouse cursor, making it convenient to access AI services.`,
  what_is_writing_assistant: 'What is Writing Assistant?',
  writing_assistant_intro: `Writing Assistant is an advanced tool that harnesses the capabilities of robust language models to enhance your writing experience with a 10-fold improvement in speed and quality. It offers assistance in various ways.
    1. Content Generation: Easily generate emails, blogs, social media posts, and more by providing a topic or outline.
    2. Enhanced Expression: Improve the clarity and style of your text by utilizing the tool to rewrite and rephrase sentences.
    3. Comprehensive Content Creation: Expand on ideas and concepts to create more comprehensive and detailed content.
    4. Efficient Communication: Craft responses for emails, posts, messages, or comments effortlessly. Understand the sender's intent and reply with the appropriate tone and content.`,
  what_is_contextual_toolbar: 'What is Contextual Toolbar?',
  contextual_toolbar_intro: `The context toolbar is a toolbar that appears after selecting text on a webpage or editor. 
    It includes an AI assistant button; clicking it activates the AI assistant. Simultaneously, the selected text is passed to the AI assistant as the working context, enabling it to perform specific tasks based on the selected text. 
    Additionally, the toolbar offers other useful functions, such as a copy button for copying selected text to the clipboard and a webclip button that, when clicked, saves the selected text to a Memo.`,
  what_is_sidebar: 'What is Sidebar?',
  sidebar_intro: `The FunBlocks AI sidebar offers a convenient way to quickly utilize FunBlocks AI assistant. When hovering over the sidebar FunBlocks icon, the following functional buttons are displayed:
    1. Page AI Assistant: Helps you quickly read and understand the current page's content. Automatically retrieves the page text as the AI assistant's working context, facilitating immediate dialogue with the AI assistant about the page content.
    2. Global AI Assistant: Not limited to the current page's content, allows for free interaction and collaboration with the AI assistant.
    3. Quick Settings: Provides selected settings options for commonly used functions, allowing for easy personalization adjustments.
    You can change the sidebar hover position by dragging it up or down.`,
  what_is_prompt_app: 'What is Prompt App?',
  prompt_app_intro: `Funblocks AI Assistant has powerful customization capabilities, allowing users to effortlessly create personalized Prompt applications.
    Creating a Prompt application is straightforward—simply provide the Prompt for LLM, and you can easily utilize it in the AI Assistant.
    Moreover, Funblocks allows the addition of custom variables within the Prompt, enabling the AI Assistant to have enhanced functionality.
    You can also publish self-created Prompt applications to FunBlocks Prompt Store, providing other users with the option to choose and experience innovative AI Assistant functionalities.`,
  to_prompts_editor: 'Add Prompt',
  settings_memo: 'Memo',
  what_is_memo: 'What is Memo?',
  memo_intro: `FunBlocks offers the functionality to save AI-generated content or captivating selections from web pages to Memos.
    Memos allow you to effortlessly store interesting content at any time, creating a personalized knowledge collection.
    These Memos serve as the foundation for your personalized AI assistant.
    Within FunBlocks, you can easily browse through saved Memos. You have the flexibility to continue editing Memo content at any time, enjoying a creative experience comparable to Notion.`,
  to_memo: 'To Saved Memos',
  settings_shared_aigc: 'AIGC Community',
  share_aigc_intro: `When AI generates wonderful content, you can share it with friends or post it to FunBlocks' AIGC community.
    Sharing exceptional AIGC content also comes with extra rewards, where you'll earn 10 AI coins each time it's read.`,
  space_intro: 'FunBlocks Workspace',
  to_workspace: 'To Workspace',
  what_is_workspace: 'What is FunBlocks Workspace?',
  workspace_intro: `FunBlocks Workspace is an all-in-one knowledge hub where you can create, organize, and collaborate on documents, presentations, and mind maps.  Powered by an AI assistant that learns and adapts to your needs, the workspace becomes your personalized knowledge base, boosting your productivity and streamlining creative workflows.`,

  close_ai_modal_confirm:
    'When closing the AI assistant or rolling back AI-generated results, no confirmation is needed',
  never_show: 'Auto confirm in the future',
  settings_more: 'More settings',

  disable_confirm: 'Disable Confirm',
  disabled_pages: 'List of websites where {widget} is disabled',
  disable_this_visit: 'Disable until next visit',
  disable_this_page: 'Disable for this site',
  disable_globally: 'Disable globally',
  view_all: 'View all',
  can_reopen: 'You can re-enable it in',

  err_video_no_subtitles: 'This video does not provide readable subtitles.',
  err_failed_get_subtitles: 'Failed to retrieve subtitles. Please try again.',
  err_unknown_video: 'Unknown video.',

  add_prompt: 'Create App',
  task_content: 'Variables input',
  task_content_from_selected: 'Selected text from page',
  task_content_from_input: 'Text input',
  task_content_from_form: 'Custom form fields',
  task_content_form: 'Custom form fields',
  prompt_title: 'Title',
  developer: 'Developer',
  description: 'Description',
  prompt_desc_placeholder:
    'A description of this AI App/Agent, for example, what it can do, etc...',
  prompt_template: 'Prompt template',
  prompt_template_desc:
    'The prompt for AI, which should contain {variables}, will be replaced by {content_source} when the AI ​​assistant executes it',
  prompt_template_fixed_desc: 'The prompt for AI',
  prompt_template_placeholder: `Prompt, for example: I want you to act as a creative writing assistant. The following is the first part of the article. Please understand the author's writing intention and continue to write the next paragraph. It is required to maintain a consistent style, coherent and logical content. The given text is {{var_0}}`,
  prompt_template_fixed_placeholder: `Prompt, for example: Tell me a funny joke. `,
  prompt_no_text_err:
    'Make sure to include {variables} in your prompt template',
  prompt_content_placeholder: 'Text for AI assistant',
  prompt_content_label: 'Topic',
  prompt_context: 'Prompt context',
  prompt_context_desc:
    'Provide relevant content for the AI assistant to execute prompt',
  context_no_doc_choosen: 'Please choose a page for AI assistant as context',
  context_doc_no_content: 'The selected page has no readable content',
  context: 'Given context',
  current_doc: 'Current page',
  choose_a_doc: 'Choose a page',
  none: 'None',
  settings_space_menu: 'Settings',
  settings_space_title: 'Workspace Settings',
  settings_members_menu: 'Members',
  settings_labs_menu: 'Labs',
  settings_db_capability_switch: 'Database enabled',
  service_subscribe: 'Upgrade',
  service_subscribe_title: 'Upgrade plans',
  service_product_name: '{service} {level}',
  service_market: 'Buy AI Token',
  price: 'Price',
  num: 'Quantity',
  name: 'Name',

  pin: 'Pin',
  pinned: 'Pinned',
  visibility: 'Visibile to',
  visibility_private: 'Only me (Private)',
  visibility_public: 'Everyone (Public)',
  visibility_workspace: 'Everyone (@workspace)',
  prompts_mine: 'My Apps',
  prompts_mine_desc:
    'You can create your own AI assistant apps here or choose from public apps that suit your needs.',
  prompts_developed: "My Apps (I've developed)",
  prompts_public: 'Public Apps',
  prompts_workspace: 'Workspace Apps',
  prompts_pinned: 'Pinned Apps',
  prompts_pinned_desc:
    'You can pin your favorite apps from public or shared apps, and the AI assistant will display your pinned apps in the menu, ready for you to execute at any time.',
  prompts_drafter: 'FunBlocks AI apps',
  prompts_validate: 'Validate Apps',
  prompt_qualified: 'Qualified',
  prompt_not_qualified: 'Not Qualified',
  prompt_no_arg: 'No variable in prompt',
  run_prompt: 'Run',
  prompt_language: 'App description language',
  all: 'All',
  arg_type: 'Type',
  arg_type_textline: 'Text',
  arg_type_text: 'Multiline text area',
  arg_type_select: 'Select',
  select_option_placeholder: 'Option for select',
  select_option_free_input: 'Allow users to enter values other than options',
  no_suitable_option: 'No suitable option',
  user_input_option: 'Input your own value',
  arg_name: 'Variable',
  arg_label: 'Title',
  arg_hint: 'Hint',
  arg_default_value: 'Default value',
  arg_required: 'Required',
  arg_optional: 'Optional',
  yes: 'Yes',
  no: 'No',
  arg_label_placeholder: 'Field Title',
  arg_hint_placeholder:
    'Show user the appropriate content to fill in this field',
  arg_default_value_placeholder: 'Default value',
  add_arg: 'Add Field',
  clear_arg: 'Clear',
  update: 'Update',
  generate_outline: 'Generate outline with AI',
  generate_outline_tooltip:
    'Let AI generate an outline first, and modify it as needed before instructing AI to generate the full text',
  generate_email_reply_outline_tooltip:
    'Let AI consolidate the email requirements and formulate key response points',
  no_topic: 'Please provide a topic to write about',
  prompt_blocked_safety:
    'Contain dangerous or inappropriate information in task prompts',
  unknown_failure: 'Unknown failure',
  summarize_web_page: 'Summarize web page',
  summarize_video: 'Summarize video',
  video_assistant: 'Video AI',

  extension_not_work_in_funblocks_site:
    'The FunBlocks AI browser extension is designed specifically for websites outside of FunBlocks. For the FunBlocks main site, please use the FunBlocks AI Assistant directly.',

  prompt_unpin_tooltip: 'Remove it from my AI assistant',
  prompt_pin_tooltip: 'Pin it to my AI assistant',
  prompt_clone_tooltip: 'Clone App',
  prompt_edit_tooltip: 'Edit App',
  prompt_delete_tooltip: 'Delete App',
  prompt_share_tooltip: 'Share App through link',
  prompt: 'Prompt',
  confirm_open_ai_generated_slides:
    'The slideshow has been generated and saved in private space, continue to edit?',

  CRUD: 'Add/Update/Delete',
  not_provided: 'Not provided',
  new: 'New',
  clone: 'Clone',

  upgrade_to_vip: 'Upgrade your plan',
  purchase_ai: 'Purchase AI Premium',
  cancel_subscription: 'Cancel current plan',
  billed_period: 'Billing period',
  trial_vip: '{trial_days} days FREE trial',
  trial_desc:
    'You will have a free trial of VIP plan for {trial_days} days, and there will be no charges during this period.',

  buyable_plans: 'Funblocks Plans',
  privileges: 'Privileged features',
  privileged_feature: 'This feature is privileged to VIP plan',
  current_plan: 'Current plan',
  plan_expire_at: 'Current plan will expire at: {date}',
  last_plan_expired: 'Your last {plan} plan expired at {date}, renew it now!',
  buy: 'Buy now',
  paid_to_check: 'Paid, refresh status',
  subscribe_status_updated: 'Subscription status updated',
  aicoins_status_updated: 'AI tokens balance updated',
  goto_vip_buy_page: 'Upgrade to VIP/Buy AI Token',
  aicoin: 'AI Coins',
  exceeded_quota: 'You have reached the current plan usage limit.',
  promote_trial: 'Start 3 days free trial',

  feature_for_members:
    'This feature requires the use of FunBlocks services, so please register or login to your FunBlocks account first.',
  confirm_logon:
    'Have you completed the registration or login for your FunBlocks account?',
  unlogin_user: 'Guest user',
  login_signin_form_info: 'Sign In to your account',
  login_signin_vcode_form_info: 'Login or create your FunBlocks account',
  login_resetpswd_form_info: 'Reset your password with verfication code',
  login_signup_form_info: 'Register your account to FunBlocks',
  forgotpswd: 'Forgot password?',
  hadaccount: 'Already have an account?',
  login: 'Login',
  password_login: 'Login with password',
  vcode_login: 'Login with verification code',
  logout: 'Logout',
  signup: 'Sign Up',
  resetpswd: 'Reset Password',
  register_now: 'Register Now',
  signup_info: `Don't have a FunBlocks account yet? You can complete the registration in one minute, and immediately experience the fun of writing with FunBlocks AI.`,
  getvcode: 'Get Code',
  verification_code: 'Verification Code',
  vcode_err: `Code incorrect, if it's unclear, you can click it to refresh`,
  phone: 'Phone',
  email: 'Email',
  phone_or_email: 'Phone or Email',
  nickname: 'Nick name',
  captcha: 'Captcha',
  password: 'Password',
  connected_account: 'Connected FunBlocks Account',

  confirm_delete_item:
    'It cannot be restored after deletion. Are you sure to delete?',

  month: 'Month',
  week: 'Week',
  day: 'Day',
  quarter: 'Quarter',
  year: 'Year',

  account_not_activated:
    'Your account is not activated, the activation email has been sent to your email: {email}, please check the email and click the link to activate your account.',
  no_activation_email:
    'If you have not received the activation email, you can click this button',
  still_no_activation_email: 'You can also click this button',
  resend_activation_email: 'Resend activation email',
  try_another_way: 'Try another registration method',
  already_activated: 'If you have already activated, please click this button',

  delete: 'Delete',
  done: 'Done',
  nothing_in_trashbin: 'Nothing in Trashbin',
  delete_all: 'Empty Trashbin',
  restore: 'Restore',
  confirm_title: 'Confirm',
  confirm_delete_content: 'Are you sure to delete? ',
  confirm_delete_doc:
    'Are you sure to delete this item? Delete from trashbin has no way to find it back.',
  confirm: 'Confirm',
  cancel: 'Cancel',
  release_to_delete: 'Release to put file to trashbin',
  remove: 'Remove',
  title_input: 'Please input title/topic for AI assistant',

  drag: 'Drag ',
  to_move: 'to move',
  click: 'Click',
  to_open_menu: 'to open menu',

  member: 'Members',
  user: 'User',
  role: 'Role',
  admin: 'Admin',
  invite: 'Invite',
  add_user: 'Add Member',
  invite_user: 'Invite Members',

  to_read: 'To Read',
  read: 'Read',
  note: 'Note',
  copy: 'Copy',

  add_ril_tooltip:
    'Add links copied from other Apps or websites to Read It Later',
  ril_tooltip: 'Articles from other Apps or websites',

  accept: 'Accept',
  reject: 'Reject',

  cmd_trigger: 'is what I want',
  cmd_ai_optimize: 'AI optimize text',
  cmd_ai_continue: 'AI continue write',
  cmd_ai_assistant: 'Ask AI...',

  cmd_trigger_desc: 'Continue with text',
  cmd_ai_optimize_desc: 'AI optimize text',
  cmd_ai_continue_desc: 'AI continues to write based on the above content',
  cmd_ai_assistant_desc: 'Review, edit, or write',

  missing_required_data: 'Missing required info',
  missing_one_data: 'Please input at least one piece of information.',

  text: 'Text',

  askAI: 'Choose an AI task below, or prompt to AI directly...',
  askAI_next: 'Choose action below, or tell AI what to do next...',
  askAI_doing: 'AI is writing ',
  askAI_directly_tooltip:
    'Click the button to directly command the AI assistant with the entered content or choose the corresponding operation from the menu below',
  draft_placeholder:
    'Please enter the subject and specific requirements (such as word count, main points, etc.)...',
  fill_in_selected: 'Fill in selected text',
  sendAI: 'Generate',
  reset_input: 'Reset',
  draft_artile: 'Write a',
  topic_label: 'about',
  outline_prompt:
    'You are going to write an {article} with the subject: ```{topic}```, key points: ```{key_points}```. Please draw up a clear outline',
  draft_more_type: 'Article type',

  selection_text: 'Selection text:',
  ai_response: 'AI wrote:',

  replace_selection: 'Accept',
  replace_title: 'Replace title',
  insert_title: 'Insert title',
  insert_below: 'Insert below',
  copy_generated_content: 'Copy',
  save_to_memo: 'Save to Memo',
  saved_to_memo: 'Saved to FunBlocks Memo',
  copy_and_close: 'Copy and close',
  continue_writing: 'Continue writing...',
  make_longer: 'Make longer',
  try_again: 'Try again',
  discard: 'Discard',
  confirm_close_ai:
    'Are you sure to close AI assistant? The unsaved AI generated content will be discarded.',
  confirm_discard_ai_content:
    'Are you sure to step back? The unsaved AI generated content will be discarded',
  confirm_no_wait: `The AI assistant is writing, are you sure you won't have to wait a while?`,
  text_too_long: 'The selection text is too long',
  no_text: 'No content for AI',
  ai_timeout: 'AI timeout',
  should_select_item: 'Please select item from action menu below',
  should_text_or_select_item:
    'Please select item from action menu below or text to ask AI directly',
  ai_response_warning: 'AI response can be inaccurate or misleading',
  ai_draft: 'AI Write',
  ai_reply: 'AI Reply',
  ai_read: 'AI Read',
  ai_comment: 'AI Comment',
  ai_writing_assistant: 'AI Writing Assistant',

  like_this_extension:
    'If you enjoy our service, please share your experience and give us a five-star rating to help more people discover it!',
  go_rating: 'Leave a Review Now',
  feedback_here: 'Suggestions for the product? Report any issues?',
  generate_lang:
    'Generate language settings, defaulting to the extension language settings',

  explain: 'Explain',
  translate: 'Translate',
  optimize: 'Optimize',
  continue: 'Continue...',
  other: 'Other',

  share_aigc_reward_tooltip:
    'Share AI creations, get 10 free AI queries for each view',
  share_aigc: 'Share AI Co-creations with Friends or AIGC Community',
  share_to: 'Post to',
  aigc_community: 'FunBlocks AIGC Community',

  your_ai_queries: 'Your AI Queries',
  seems_not_enough: 'Seems not enough?',
  get_more: 'Get more!',

  ai_token: 'AI Token',
  ai_token_desc: 'One AI token can cover one AI query',
  your_ai_token_balance: 'You AI Token Balance: ',
  ai_tokens_income: 'AI Token Income Transations',
  show_latest_records: 'Only show the latest {count} items',
  date: 'Date',
  count: 'Amount',
  income_item: 'Income Item',
  new_user_trial: 'Trial',
  invited_by_friend: 'Invited By Friend Reward',
  invite_friend: 'Invite Friend Reward',
  shared_aigc_visited: 'Shared AIGC Visited Reward',
  payment: 'Buy',
  wanna_more_ai_tokens: 'Get more AI tokens: ',
  to_resize: 'to resize',
  launch_reading_assistant_tooltip: 'Open Reading Assistant',
  launch_mindmap_tooltip: 'Dive deep into video content with AI Flow Mindmap',
  breakdown_topic: 'Breakdown topic with AI Flow',
  ai_flow_explore: 'Explore topics related to page content with AI Flow',
  ai_flow_question: 'Explore any topic or question with AI Flow',
  ai_flow_image: 'Exploring Image Content with AI Flow',
  copy_link: 'Copy share link',
  screenshot: 'Screenshot',
  download: 'Download',
  invalid_api_for_image:
    'AI Assistant does not currently support querying image information from third-party LLM services. Please select FunBlocks AI service.',
  softbreak_tips: "Use 'Shift + Enter' for new line",
  ai_task: 'Selected AI Task',
  reflect: 'Reflect',
  change_tone: 'Tone',
  launch_summary_tooltip:
    'For videos with subtitles, you can summarize the video content with one click',
  enable_ai_use_search: 'Enable web search',
  disable_ai_use_search: 'Disable web search',
}
