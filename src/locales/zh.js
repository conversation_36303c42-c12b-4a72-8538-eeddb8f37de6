export default {
  title: '标题',
  ril: '稍后阅读',
  back: '返回',
  close: '关闭',
  cancel: '取消',
  confirm: '确认',
  open: '打开',
  create: '创建',
  confirm_done: '确认',
  loadmore: '加载更多',
  refresh: '刷新',

  export: '导出',
  import: '导入',

  error_msg: '抱歉，出错啦!',

  invite_code: '邀请码',
  invite_friends: '邀请好友',
  invite_friends_desc: '奖励FunBlocks AI币',
  or_invite_friend_rewards: '或者邀请好友，获得奖励',
  copy_intruction: '复制以下内容发送给朋友:',
  homepage: '访问主页',
  feedback: '建议或问题反馈',
  reply: '回复',
  notice: '通知',
  new_version_available: '发现新版本，请点击下方按钮更新',
  upgrade_now: '立即更新',
  upgrade_plan: '升级会员',
  reload: '重新加载',

  slides: '幻灯片',

  share: '分享',
  edit: '编辑',

  copy_link: '复制链接',
  duplicate: '复制',
  copied: '已复制到剪切板',
  copied_also: '内容已同时复制到剪切板',
  copied_to_share: '分享内容已复制，您可以粘贴到分享区域',
  copy_failed: '复制失败',

  slides_ai_converter: '由AI转成幻灯片',

  open_to_public: '公开页面给所有人',
  everyone_at_org: '空间全体成员',
  everyone_at_group: '小组所有成员',

  ril: '稍后阅读',
  settings: '设置',
  quick_settings: '快捷设置',
  settings_account: '账号相关',
  settings_workspace: '空间相关',

  my_prompt: '我的指令',
  assistant_reader: '阅读助手',
  assistant_writer: '写作助手',
  assistant_with_page_context: '可获取页面内容的AI助手',
  assistant_without_page_context: '与页面内容无关的AI助手',
  ask_ai_about_selected: '选定内容',
  ask_ai_about_selected_tooltip: '指定AI处理选定的文本',
  ask_ai_about_given_text_hint: '请输入或粘贴要处理的文本',
  ask_ai: '打开 FunBlocks AI 助手',
  selected_text: '选定的文本',
  selected_whole_page: '已选定文章内容',
  selected_subtitles: '已选定视频脚本',
  selected_email: '收到的邮件内容',
  injected_ai_trigger_placeholder: '输入 {trigger} 可唤起 FunBlocks AI 助手',
  settings_ai_trigger: 'AI 助手命令符',
  settings_ai_trigger_desc: `在任意网页输入框或编辑器中，输入{trigger}，即可唤起 AI 助手`,
  settings_ai_trigger_none_desc:
    '设置命令符之后，在页面输入框或编辑器任意位置输入命令符，即可唤起 AI 助手',
  settings_ai_trigger_conflict_warn: `'/'更方便输入，但如果您经常使用 Notion 等以'/'作为命令符的编辑软件，建议选择'\\'或'\\\\'`,
  balloon_auto_hide: `菜单5秒定时隐藏`,
  balloon_copy_enabled: `支持“复制”按钮`,
  balloon_auto_display: '自动展示',
  balloon_auto_display_desc: '在选中任何文本时显示智能菜单',
  balloon_shortcut_display: '快捷键展示',
  balloon_shortcut_select_text: '当选中任何文本后',
  balloon_shortcut_plus_hotkey: '按下 {hotkey} 显示智能菜单',

  settings_my_account_menu: '我的账户',
  settings_my_account_title: '我的账户',
  settings_language_menu: '语言',
  settings_language_title: '语言设置',
  settings_api_enable_api: '直接访问大语言模型(LLM) API',
  llm_api_token_guide: '您可以登录{aiProvider}网站，获取API Key',
  settings_language_desc: '插件用户界面语言',
  settings_ai_respond: 'AI 默认输出语言',
  settings_ai_respond_desc:
    'AI 默认输出语言设置主要应用于 AI 阅读助手功能，例如"解释"和"翻译"',
  settings_translate_to: '“翻译”目标语言',
  set_lang_as_context: '根据上下文语言',
  set_default_ai_model: '设置默认 AI 模型',
  set_ai_model: '设置大语言模型',
  private_llm_key_list: '私有LLM API Key列表',
  add_private_llm_key: '添加私有LLM API Key',
  self_owned_ai_api: '私有LLM API Key',
  get_models_test_token: '获取模型列表/测试 API Key',
  token_valid: 'Key 和 端口 已验证',
  token_invalid: '请检查 Key 或 端口 是否正确',
  settings_general: '通用',
  settings_api_menu: 'LLM 服务提供方',
  settings_api_title: '大语言模型(LLM)接口设置',
  settings_api_llms: '支持直接调用的LLM服务方',
  settings_api_choose_model: '选择模型',
  settings_api_proxy: '服务地址(或代理地址)',
  settings_api_desc:
    '### 您已经选择 {provider} 作为 AI 服务提供者：\n1. FunBlocks AI将以您提供的API Key直接访问 {provider}，不受FunBlocks AI访问次数限制。 \n2. API Key在本地保存，不会上传到FunBlocks服务端，无需担心被盗用。',
  openai_compatible:
    '### 使用兼容OpenAI接口的LLM服务时，只需：\n1. “服务地址”中输入接口地址；\n2. 选择“其他”模型，并填入模型名称。',
  settings_funblocks_desc:
    '注册 FunBlock 账号后，您将获得 40 次 AI 助手服务的试用次数，并每天享有 20 次免费的 AI 助手服务。此外，您还可选择升级至 FunBlock AI 会员计划，以获取无限次的 AI 助手访问。',
  invalid_api_settings:
    '请在设置里注册或登录 FunBlocks 帐号，或选择第三方 AI 大模型服务提供方。',
  confirm_api_set: '确认已完成设置？',
  has_ai_api: '有LLM API Key?',
  settings_prompts_menu: '自定义AI Prompts',
  settings_prompts_title: '自定义AI应用管理',
  settings_side_bar: '侧边工具栏',
  settings_side_bar_desc:
    '开启后，侧边工具栏将一直显示在浏览器的右侧或左侧，方便快速启动FunBlocks AI助手。',
  settings_side_bar_align: '显示于浏览器',
  left_side: '左侧',
  right_side: '右侧',
  settings_quick_compose: '写作助手',
  settings_quick_compose_desc:
    '开启“写作助手”后，当页面输入框或编辑器获得光标焦点时，会在右侧显示快捷按钮，点击即可调出“AI写作助手”。',
  settings_quick_action: '智能菜单',
  settings_quick_action_desc: `开启“智能菜单”后，选中页面或编辑框中的文本时，会在鼠标下方显示“智能菜单”，方便使用AI服务。`,
  what_is_writing_assistant: '什么是写作助手?',
  writing_assistant_intro: `FunBlocks Writing Assistant是一款先进的工具，利用强大的大语言模型的能力，以10倍的速度和质量改进您的写作体验。它通过多种方式提供帮助：
    1. 内容生成： 通过提供主题或大纲，轻松生成电子邮件、博客、社交媒体帖子等。
    2. 表达提升： 利用工具重写和改写句子，提高文本的清晰度和风格。
    3. 全面内容创作： 拓展思想和概念，创作更为全面和详细的内容。
    4. 高效沟通： 轻松为电子邮件、帖子、消息或评论撰写回应。理解发件人的意图，并以合适的语气和内容回复。`,
  what_is_contextual_toolbar: '什么是智能菜单？',
  contextual_toolbar_intro: `智能菜单是在网页或编辑器中选择文本后出现的菜单。
    它包含一个AI助手按钮，点击即可启动AI助手。同时，选中的文本会作为工作上下文传递给AI助手，让它根据选中的文本完成具体任务。
    此外，工具栏还提供其他实用功能，如复制按钮，用于将选中的文本复制到剪贴板，以及剪报按钮，点击可将选中的文本保存到Memo中。`,
  what_is_sidebar: '什么是侧边工具栏？',
  sidebar_intro: `FunBlocks AI 侧边工具栏提供了便捷的方式来迅速使用AI助手。当鼠标悬停在侧边工具栏上时，会显示以下功能按钮：
    1. 页面AI助手：能够帮助您快速阅读和理解当前页面内容。自动获取页面文本作为AI助手的工作上下文，实现与AI助手关于页面内容的即时对话。
    2. 全局AI助手：不限于当前页面内容，可与AI助手进行自由互动和工作。
    3. 快捷设置：提供了精选常用功能的设置选项，方便进行个性化调整。
    可以通过上下拖动来改变悬停位置。`,
  what_is_prompt_app: 'Prompt 应用是什么？',
  prompt_app_intro: `Funblocks AI 助手具备强大的自定义能力，让用户轻松创建个性化的 Prompt 应用。
    创建 Prompt 应用极为简单，只需提供与LLM的 Prompt 即可，即可在 AI 助手中轻松使用。
    同时，Funblocks 还允许在 Prompt 中添加自定义变量，让 AI 助手在执行时呈现更丰富的用户界面，实现更强大的功能。
    用户还可将自己创建的 Prompt 应用发布到 Prompt 应用市场，供其他用户选择使用，为更多用户带来创新的 AI 助手功能。`,
  to_prompts_editor: '创建 Prompt 应用',
  settings_memo: '即时笔记',
  what_is_memo: '什么是即时笔记(Memo)？',
  memo_intro: `FunBlocks 提供了将 AI 生成的内容或在网页中选中的精彩内容保存到 Memo 的功能。
    Memo 让您随时轻松保存感兴趣的内容，形成个性化的知识集合。
    这些 Memo 将成为您个性化 AI 助手的知识基础。
    在 FunBlocks 中，您可以方便地浏览已保存的 Memo，也可享受与 Notion 相媲美的创作体验，随时继续编辑 Memo 内容。`,
  to_memo: '查看保存的即时笔记(Memo)',
  settings_shared_aigc: 'AIGC 社区',
  share_aigc_intro: `当AI生成了精彩内容，你可以分享给朋友，也可以分享到FunBlocks的AIGC社区。
    分享精彩的AIGC还有额外奖励，每次阅读您都将获得10个AI币。`,
  space_intro: 'FunBlocks 工作空间',
  to_workspace: '进入工作空间',
  what_is_workspace: 'FunBlocks 工作空间是什么？',
  workspace_intro: `FunBlocks Workspace 是一个一体化的知识工作空间，您可以在其中创建、组织文档、演示文稿和思维导图，并进行协作。 在人工智能助手的支持下，工作区会学习并适应您的需求，成为您的个性化知识库，从而提高您的工作效率并简化创意工作流程。`,

  close_ai_modal_confirm: '关闭AI助手或回退AI生成结果时，无需确认',
  never_show: '以后无需确认，直接关闭或返回',
  settings_more: '更多设置',

  disable_confirm: '禁用确认',
  disabled_pages: '禁用{widget}的网站列表',
  disable_this_visit: '在本次访问中禁用',
  disable_this_page: '在本网站中禁用',
  disable_globally: '在所有网站禁用',
  view_all: '查看所有',
  can_reopen: '您可以在设置里重新开启',

  err_video_no_subtitles: '本视频没有提供可读脚本',
  err_failed_get_subtitles: '获取脚本失败，请重试',
  err_unknown_video: '未知视频',

  add_prompt: '添加应用',
  developer: '开发者',
  description: '描述',
  task_content: 'Prompt变量来自',
  task_content_from_selected: '文档中选定的内容',
  task_content_from_input: '单文本框输入',
  task_content_from_form: '自定义表单输入',
  task_content_form: '自定义表单',
  prompt_template: 'AI Prompt',
  prompt_title: '标题',
  prompt_desc_placeholder: '对AI应用的描述，例如，它能做什么等...',
  prompt_template_desc:
    '对AI的提示语(Prompt)，在其中应包含{variables}，AI助手在执行时，会将其替换成{content_source}',
  prompt_template_fixed_desc: '对AI的提示语(Prompt)',
  prompt_template_placeholder: `对AI的Prompt内容,例如：我希望你扮演一个创意作家的角色。我将为你提供一个段落，你的任务是根据其意图进行续写。在保持原文的语气和风格的基础上，注入你的想象力和连贯性。你的续写应该为故事增添深度和细节，吸引读者的注意力和兴趣。给定段落如下：{{var_0}}，请开始写作`,
  prompt_template_fixed_placeholder:
    '对AI的Prompt内容,例如：我希望你扮演一个脱口秀演员。给我说个笑话吧',
  prompt_no_text_err: `请确认你的Prompt中包含：{variables}`,
  prompt_content_placeholder: '让AI处理的主题或内容',
  prompt_content_label: '主题',
  prompt_context: '指令上下文',
  prompt_context_desc: '为AI助手执行指令提供相关内容',
  context_no_doc_choosen: '请选择相关页面',
  context_doc_no_content: '选择的页面没有可读取内容',
  context: '相关素材',
  current_doc: '当前页面',
  choose_a_doc: '选择一个页面',
  none: '无',
  settings_space_menu: '空间设置',
  settings_space_title: '空间设置',
  settings_members_menu: '成员管理',
  settings_labs_menu: '实验室',
  settings_db_capability_switch: '多视图数据库功能',
  service_subscribe: '升级会员',
  service_subscribe_title: '升级会员资格',
  service_product_name: '{service}{level}会员',
  service_market: '购买 AI 币',
  price: '价格',
  num: '数量',
  name: '名称',

  pin: '收藏',
  pinned: '已收藏',
  visibility: '对谁可见',
  visibility_private: '自己(私有)',
  visibility_public: '所有人(公共)',
  visibility_workspace: '同事(同一工作空间)',
  prompts_mine: '我的应用',
  prompts_mine_desc:
    '你可以在这里制作自己的AI助手应用，或者从公共应用中选择适合你的应用。',
  prompts_developed: '我开发的应用',
  prompts_public: '公开应用',
  prompts_workspace: '工作空间应用',
  prompts_pinned: '精选的应用',
  prompts_pinned_desc:
    '您可以从公开或共享的应用中收藏您喜欢的，AI助手会在菜单中显示您收藏的应用，随时供您选择执行。',
  prompts_validate: '待审核应用',
  prompts_drafter: '内置撰写类应用',
  prompt_qualified: '合格',
  prompt_not_qualified: '不合格',
  prompt_no_arg: '固定Prompt，不包含用户输入内容',
  run_prompt: '执行',
  prompt_language: '应用描述语言',
  all: '所有',
  arg_type: '类型',
  arg_type_textline: '文本框',
  arg_type_text: '多行文本框',
  arg_type_select: '下拉选择',
  select_option_free_input: '允许用户自行输入选项之外的值',
  no_suitable_option: '没有合适的选项',
  user_input_option: '输入你自己的选项值',
  select_option_placeholder: '选项',
  arg_name: '变量名',
  arg_label: '标题',
  arg_hint: '提示',
  arg_required: '必填',
  arg_optional: '可选',
  arg_default_value: '默认值',
  yes: '是',
  no: '否',
  arg_label_placeholder: '表单项名称',
  arg_hint_placeholder: '向用户展示填入此表单项的合适内容，可以举例',
  arg_default_value_placeholder: '展示给用户的默认值',
  add_arg: '增加表单项',
  clear_arg: '重置',
  update: '更新',
  generate_outline: '让AI生成提纲',
  generate_outline_tooltip: '让AI先生成提纲，可以根据需要修改后再让AI生成全文',
  generate_email_reply_outline_tooltip: '总结邮件中的需求，生成回复要点',
  no_topic: '请提供要撰写的主题',
  prompt_blocked_safety: '在任务提示中包含危险或不当信息',
  unknown_failure: '未知错误',
  summarize_web_page: '总结网页内容',
  summarize_video: '总结视频内容',
  video_assistant: 'AI视频助手',

  extension_not_work_in_funblocks_site:
    'FunBlocks AI浏览器扩展是专为FunBlocks之外的网站而设计的。在FunBlocks主站，请直接使用FunBlocks AI助手。',

  prompt_unpin_tooltip: '取消应用收藏，并从AI助手菜单移除',
  prompt_pin_tooltip: '收藏应用，并展现在AI助手菜单中',
  prompt_clone_tooltip: '复制/克隆应用',
  prompt_edit_tooltip: '编辑应用',
  prompt_delete_tooltip: '删除应用',
  prompt_share_tooltip: '通过链接分享应用',
  prompt: 'Prompt',
  confirm_open_ai_generated_slides: '已生成幻灯片，保存在私有空间，继续编辑？',

  CRUD: '增/删/改',
  not_provided: '未提供',
  new: '新建',
  clone: '复制/克隆',

  checkout_form_title_aiplus: '获得更强大的FunBlocks AI',
  checkout_form_title_funblocks: '升级到{level}会员',
  checkout_total: '总计',

  upgrade_to_vip: '升级您的会员',
  purchase_ai: 'Purchase AI Premium',
  cancel_subscription: '取消会员订阅',
  billed_period: '订阅周期',
  trial_vip: '{trial_days}天免费试用',
  trial_desc: '您将免费试用VIP会员{trial_days}天，在次期间无需付费',

  buyable_plans: '可订阅的会员产品',
  privileges: '会员功能',
  privileged_feature: '此功能为会员功能',
  current_plan: '当前会员等级',
  plan_expire_at: '有效期至 {date}',
  last_plan_expired: '你的{plan}会员已于{date}过期',
  buy: '立即购买',
  paid_to_check: '如果已支付，请点击按钮',
  subscribe_status_updated: '会员状态已更新',
  aicoins_status_updated: 'AI币余额已更新',
  goto_vip_buy_page: '加入会员/购买AI币',
  aicoin: 'AI币',
  exceeded_quota: '您已经达到当前会员使用限量',
  promote_trial: '免费试用',

  feature_for_members:
    '此功能需要使用FunBlocks相关服务，故请先注册或登录FunBlocks帐号。',
  confirm_logon: '是否已完成FunBlocks帐号注册或登录？',
  unlogin_user: '访客模式',
  login_signin_form_info: '登录您的FunBlocks账号',
  login_signin_vcode_form_info: '登录或创建您的FunBlocks账号',
  login_resetpswd_form_info: '输入验证码重置您的密码',
  login_signup_form_info: '注册您的FunBlocks账号',
  forgotpswd: '忘记密码？',
  hadaccount: '已有账号？',
  login: '登录',
  vcode_login: '邮箱验证码登录',
  password_login: '密码登录',
  logout: '退出登录',
  signup: '注册',
  resetpswd: '重置密码',
  register_now: '立即注册',
  signup_info:
    '还没有FunBlocks账号？一分钟即可完成注册，立即体验使用FunBlocks AI写作和学习的乐趣。',
  getvcode: '获取验证码',
  verification_code: '验证码',
  vcode_err: '验证码错误，请重新填写，如果看不清，请点击图片换一个',
  phone: '手机号',
  email: '邮箱',
  phone_or_email: '手机号或邮箱',
  nickname: '昵称',
  captcha: '图形验证码',
  password: '密码',
  connected_account: '已关联 FunBlocks 帐号',

  confirm_delete_item: '删除后无法恢复，确认删除吗？',

  month: '月',
  week: '周',
  day: '日',
  quarter: '季度',
  year: '年',

  account_not_activated:
    '您的账户尚未激活，激活邮件已经发送至您的邮箱: {email}，请查收并点击邮件中的链接激活账户。',
  no_activation_email:
    '如果您没有收到激活邮件，可以点击右侧按钮重新发送激活邮件',
  still_no_activation_email:
    '如果您一直收不到激活邮件，可以点击右侧按钮尝试其他注册方式',
  resend_activation_email: '重新发送激活邮件',
  try_another_way: '尝试其他注册方式',
  already_activated: '如果您已经激活，请点击按钮',

  delete: '删除',
  done: '完成',
  nothing_in_trashbin: '垃圾箱没有内容',
  delete_all: '清空垃圾箱',
  restore: '恢复',
  confirm_title: '请确认',
  confirm_delete_content: '确认删除吗？',
  confirm_delete_doc: '确认删除吗？从垃圾箱中删除的内容将不可恢复。',
  confirm: '确认',
  cancel: '取消',
  release_to_delete: '在此区域释放即可删除',
  remove: '移除',
  title_input: '请在上方输入页面标题/主题，以便AI进行撰写',

  drag: '拖拽',
  to_move: '可移动位置',
  click: '点击',
  to_open_menu: '可打开菜单',

  member: '成员',
  user: '用户',
  role: '角色',
  admin: '管理员',
  invite: '邀请',
  add_user: '添加成员',
  invite_user: '邀请队员',

  workspace_name: '空间名称',

  to_read: '等待阅读',
  read: '已读',
  note: '笔记',
  copy: '复制',

  add_ril_tooltip: '添加从第三方App或网站复制的链接',
  ril_tooltip: '从第三方App或网站保存的文章',

  accept: '接受',
  reject: '拒绝',

  cmd_trigger: '是我想要的',
  cmd_ai_optimize: '润色',
  cmd_ai_continue: '续写',
  cmd_ai_assistant: '智能助手',

  cmd_trigger_desc: '输入文字',
  cmd_ai_optimize_desc: '润色',
  cmd_ai_continue_desc: '继续写...',
  cmd_ai_assistant_desc: '让智能助手修改或写作...',

  missing_required_data: '请填入必填信息',
  missing_one_data: '请输入至少一项信息',

  text: '文字',

  askAI: '选择下方 AI 任务，或告诉 AI 如何处理所选内容...',
  askAI_next: '选择下方操作选项，或告诉 AI 下一步做什么...',
  askAI_doing: '智能助手正在写 ',
  askAI_directly_tooltip:
    '点击按钮，直接用输入的内容向AI助手发送指令，或者从下面的菜单中选择相应的操作',
  draft_placeholder: '请输入主题和具体要求(如字数、要点等)...',
  fill_in_selected: '填入选中的内容',
  sendAI: 'AI 生成',
  reset_input: '重置',
  draft_artile: '写一篇',
  topic_label: '主题为',
  outline_prompt:
    '你要写一篇{article}，主题为：```{topic}```，要点：```{key_points}```。请先拟出一份清晰的提纲',
  draft_more_type: '文章类型',

  selection_text: '选中的文本:',
  ai_response: '智能助手:',

  replace_selection: '接受',
  replace_title: '替换标题',
  insert_title: '插入标题',
  insert_below: '插入生成的内容',
  copy_generated_content: '复制生成的内容',
  save_to_memo: '保存到即时笔记',
  saved_to_memo: '已保存到FunBlocks即时笔记',
  copy_and_close: '拷贝生成的内容并关闭',
  continue_writing: '继续写...',
  make_longer: '写长一点',
  try_again: '重试一次',
  discard: '丢弃',
  confirm_close_ai: '确定要关闭智能助手？未保存的生成内容将被丢弃。',
  confirm_discard_ai_content: '确定要返回上一步？未保存的生成内容将被丢弃。',
  confirm_no_wait: 'AI助手正在努力中，确定不再稍等一会儿吗？',
  text_too_long: '选中的文本太长',
  no_text: '没有提供给智能助手的内容',
  ai_timeout: 'AI助手太长时间没有响应',
  should_select_item: '请从下方菜单中选择下一步操作',
  should_text_or_select_item: '请从下方菜单中选择下一步操作或输入指令给AI',
  ai_response_warning: '智能助手返回的结果可能并不准确，在使用前请判断其正确性',
  ai_reply: 'AI 回复',
  ai_draft: 'AI 撰写',
  ai_read: 'AI 阅读',
  ai_comment: 'AI 评论',
  ai_writing_assistant: 'AI 写作助手',

  like_this_extension:
    '如果您喜欢我们的服务，请分享您的体验并给予五星好评，让更多人发现它！',
  go_rating: '现在前往留下评论',
  feedback_here: '提供产品建议或报告问题？',
  generate_lang: '生成语言设置，默认为插件设定语言',

  explain: '解释',
  translate: '翻译',
  optimize: '润色',
  continue: '续写...',
  other: '其他',

  share_aigc_reward_tooltip: '分享 AI 作品，每次阅读奖励 10 AI 币',
  share_aigc: '将 AI共创 分享给朋友或社区',
  share_to: '同时分享至',
  aigc_community: 'FunBlocks AIGC 社区',

  your_ai_queries: '你的 AI 访问',
  seems_not_enough: '不够用?',
  get_more: '获取更多!',

  ai_token: 'AI Token',
  ai_token_desc: '一个 AI币 可以支付一次 AI 任务请求',
  your_ai_token_balance: '你的 AI币 余额: ',
  ai_tokens_income: 'AI币 收入记录',
  show_latest_records: '只显示最近 {count} 条',
  date: '日期',
  count: '数量',
  income_item: '收入项目',
  new_user_trial: '新用户奖励',
  invited_by_friend: '被邀请奖励',
  invite_friend: '邀请用户奖励',
  shared_aigc_visited: '分享AI作品被阅读奖励',
  payment: '购买',
  wanna_more_ai_tokens: '获取更多 AI 币：',
  to_resize: '可以调整大小',
  launch_reading_assistant_tooltip: '打开阅读助手',
  launch_mindmap_tooltip: '用 AI Flow 深入探索视频内容',
  breakdown_topic: '用 AI Flow 拆解主题',
  ai_flow_explore: '用 AI Flow 探索页面内容相关主题',
  ai_flow_question: '用 AI Flow 探索任意主题或问题',
  ai_flow_image: '用 AI Flow 对图片内容进行探索',
  copy_link: '复制分享链接',
  screenshot: '截屏',
  download: '下载',
  invalid_api_for_image:
    'AI 助手暂不支持向第三方LLM服务询问图片信息，请选择 FunBlocks AI 服务',
  softbreak_tips: "使用 'Shift + Enter' 进行换行",
  ai_task: 'AI 助手任务',
  reflect: '判断',
  change_tone: '调整语气',
  launch_summary_tooltip: '对于有字幕的视频，可以一键总结视频内容',
  enable_ai_use_search: '启用网络搜索',
  disable_ai_use_search: '停用网络搜索',
}
