export default {
  title: 'タイトル',
  ril: '後で読む',
  back: '戻る',
  close: '閉じる',
  cancel: 'キャンセル',
  confirm: '確認',
  open: '開く',
  create: '作成',
  confirm_done: '確認済み',
  loadmore: 'さらに読み込む',
  refresh: '更新',

  export: 'エクスポート',
  import: 'インポート',

  error_msg: '申し訳ありません、エラーが発生しました!',

  invite_code: '招待コード',
  invite_friends: '友達を招待する',
  invite_friends_desc: 'FunBlocksAIコインを獲得する',
  or_invite_friend_rewards: 'または友達を招待して報酬を獲得',
  copy_intruction: '以下のコンテンツをコピーして友達に送信:',
  homepage: 'ホームページを訪問',
  feedback: 'フィードバックや問題',
  reply: '返信',
  notice: 'お知らせ',
  new_version_available:
    '新しいバージョンが見つかりました。下のボタンをクリックしてアップグレードしてください',
  upgrade_now: '今すぐアップグレード',
  upgrade_plan: 'メンバーシップをアップグレード',
  reload: 'リロード',

  slides: 'スライド',

  share: '共有',
  edit: '編集',

  copy_link: 'リンクをコピー',
  duplicate: 'コピー',
  copied: 'クリップボードにコピーされました',
  copied_also: 'コンテンツもクリップボードにコピーされました',
  copied_to_share:
    '共有用のコンテンツがコピーされました。共有エリアに貼り付けることができます',
  copy_failed: 'コピーに失敗しました',

  slides_ai_converter: 'AIによるスライド化',

  open_to_public: '一般公開ページ',
  everyone_at_org: 'ワークスペース内のすべてのメンバー',
  everyone_at_group: 'グループ内のすべてのメンバー',

  ril: '後で読む',
  settings: '設定',
  quick_settings: 'クイック設定',
  settings_account: 'アカウント設定',
  settings_workspace: 'ワークスペース設定',

  my_prompt: '自分のプロンプト',
  assistant_reader: '読書アシスト',
  assistant_writer: '執筆アシスト',
  assistant_with_page_context: 'ページコンテンツを取得できるAIアシスタント',
  assistant_without_page_context: 'ページコンテンツに関係ないAIアシスタント',
  ask_ai_about_selected: '選択したコンテンツについて',
  ask_ai_about_selected_tooltip: '選択したテキストに基づいてAIに質問します',
  ask_ai_about_given_text_hint:
    '処理するテキストを入力または貼り付けてください',
  ask_ai: 'FunBlocks AIアシスタントを開く',
  selected_text: '選択したテキスト',
  selected_whole_page: '全文を選択しました',
  selected_subtitles: '動画の字幕を選択しました',
  selected_email: '受信したメールの内容',
  injected_ai_trigger_placeholder:
    '{trigger}を入力するとFunBlocks AIアシスタントが起動します',
  settings_ai_trigger: 'AIアシスタントのトリガー',
  settings_ai_trigger_desc:
    'ウェブページやエディタで{trigger}を入力するとAIアシスタントが起動します',
  settings_ai_trigger_none_desc:
    'トリガーを設定した後、ページの入力フィールドやエディタのどこかにトリガーを入力するとAIアシスタントが起動します',
  settings_ai_trigger_conflict_warn: `'/'は入力しやすいですが、Notionなど'/'をコマンドトリガーとして使用するソフトウェアを頻繁に使用する場合は、'\\'または'\\\\\'を選択することをお勧めします`,
  balloon_auto_hide: `メニューは5秒後に非表示になります`,
  balloon_copy_enabled: `「コピー」ボタンがサポートされています`,
  balloon_auto_display: '自動表示',
  balloon_auto_display_desc:
    'テキストを選択するとスマートメニューが表示されます',
  balloon_shortcut_display: 'ショートカットキー表示',
  balloon_shortcut_select_text: 'テキストを選択した後',
  balloon_shortcut_plus_hotkey:
    '{hotkey}を押すとスマートメニューが表示されます',

  settings_my_account_menu: 'マイアカウント',
  settings_my_account_title: 'マイアカウント',
  settings_language_menu: '言語',
  settings_language_title: '言語設定',
  settings_api_enable_api: '大規模言語モデル(LLM) APIへの直接アクセス',
  llm_api_token_guide:
    '{aiProvider}ウェブサイトにログインしてAPIキーを取得できます',
  settings_language_desc: 'プラグインのユーザーインターフェース言語',
  settings_ai_respond: 'AIの出力言語デフォルト',
  settings_ai_respond_desc:
    'AIの出力言語デフォルト設定は、「説明」や「翻訳」などの読書アシスト機能に主に適用されます',
  settings_translate_to: '「翻訳」の対象言語',
  set_lang_as_context: 'コンテキストの言語に従う',
  settings_general: '一般',
  settings_api_menu: 'LLMプロバイダ',
  settings_api_title: '大規模言語モデル(LLM)インターフェース設定',
  settings_api_llms: '直接呼び出しに対応したLLMプロバイダ',
  settings_api_choose_model: 'モデルを選択',
  settings_api_proxy: 'サービスアドレス(またはプロキシアドレス)',
  settings_api_desc:
    '### {provider}をAIサービスプロバイダとして選択しました:\n1. FunBlocks AIは提供されたAPIキーを使って直接{provider}にアクセスし、FunBlocks AIのアクセス回数制限を受けません。\n2. APIキーはローカルに保存され、FunBlocksサーバーにはアップロードされないため、盗難の心配はありません。',
  openai_compatible:
    '### OpenAIインターフェースと互換性のあるLLMサービスを使用する場合は、以下の手順で設定してください:\n1. 「サービスアドレス」にインターフェースアドレスを入力します。\n2. 「その他」モデルを選択し、モデル名を入力します。',
  settings_funblocks_desc:
    'FunBlock アカウントを登録すると、AI アシスタントサービスを 40 回試用でき、毎日 20 回の無料 AI アシスタントサービスを受けられます。さらに、FunBlock AI メンバーシッププランにアップグレードすれば、無制限に AI アシスタントにアクセスできます。',
  invalid_api_settings:
    '設定で FunBlocks アカウントを登録またはログインするか、サードパーティの AI 大規模言語モデルプロバイダを選択してください。',
  confirm_api_set: '設定は完了しましたか?',
  has_ai_api: 'LLM APIキーをお持ちですか?',
  settings_prompts_menu: 'カスタムAIプロンプト',
  settings_prompts_title: 'カスタムAIアプリの管理',
  settings_side_bar: 'サイドツールバー',
  settings_side_bar_desc:
    'サイドツールバーを有効にすると、ブラウザの右側または左側にツールバーが常に表示され、FunBlocks AIアシスタントを簡単に起動できます。',
  settings_side_bar_align: 'ブラウザに表示する位置',
  left_side: '左側',
  right_side: '右側',
  settings_quick_compose: '執筆アシスト',
  settings_quick_compose_desc:
    '「執筆アシスト」を有効にすると、ページの入力フィールドやエディタにフォーカスが当たると、右側にクイックボタンが表示され、クリックすると「AI執筆アシスト」が起動します。',
  settings_quick_action: 'スマートメニュー',
  settings_quick_action_desc: `「スマートメニュー」を有効にすると、ページまたはエディタでテキストを選択するとマウスの下にスマートメニューが表示され、簡単にAIサービスを使用できます。`,
  what_is_writing_assistant: '執筆アシストとは?',
  writing_assistant_intro: `FunBlocks執筆アシストは、強力な大規模言語モデルの能力を活用し、10倍の速度と品質で書き物の体験を向上させる先進的なツールです。以下のさまざまな方法でサポートを提供します:
      1. コンテンツ生成: トピックやアウトラインを提供するだけで、メール、ブログ、ソーシャルメディア投稿などのコンテンツを簡単に生成できます。
      2. 表現力向上: ツールを使ってセンテンスを書き換え、テキストの明瞭さとスタイルを向上させます。
      3. 包括的なコンテンツ作成: アイデアやコンセプトを拡張し、より包括的で詳細なコンテンツを作成します。
      4. 効率的なコミュニケーション: メール、投稿、メッセージ、コメントなどの返信を簡単に作成できます。送信者の意図を理解し、適切なトーンとコンテンツで返信します。`,
  what_is_contextual_toolbar: 'スマートメニューとは?',
  contextual_toolbar_intro: `スマートメニューは、ウェブページやエディタでテキストを選択したときに表示されるメニューです。
      AIアシスタントボタンが含まれており、クリックするとAIアシスタントが起動します。また、選択したテキストがAIアシスタントの作業コンテキストとして渡され、選択したテキストに基づいて特定のタスクを完了します。
      さらに、ツールバーには他の便利な機能もあります。「コピー」ボタンで選択したテキストをクリップボードにコピーしたり、「クリップ」ボタンでMemoに選択したテキストを保存したりできます。`,
  what_is_sidebar: 'サイドツールバーとは?',
  sidebar_intro: `FunBlocks AIサイドツールバーは、AIアシスタントをすばやく使用するための便利な方法を提供します。サイドツールバーにマウスを合わせると、以下の機能ボタンが表示されます:
      1. ページAIアシスタント: 現在のページコンテンツを迅速に読み解くのに役立ちます。ページのテキストが自動的にAIアシスタントの作業コンテキストとして取得され、ページコンテンツに関するAIアシスタントとの即時対話が可能になります。
      2. グローバルAIアシスタント: 現在のページコンテンツに制限されず、AIアシスタントと自由に対話したり作業したりできます。
      3. クイック設定: よく使う機能の設定オプションが用意されており、個人設定を簡単に行えます。
      上下にドラッグして表示位置を調整できます。`,
  what_is_prompt_app: 'Promptアプリとは?',
  prompt_app_intro: `Funblocks AIアシスタントは高度なカスタマイズ機能を備えており、ユーザーはプロンプトアプリを簡単に作成できます。
      プロンプトアプリを作成するのは非常に簡単で、LLMのプロンプトを提供するだけです。すると、AIアシスタント内ですぐに使用できるようになります。
      さらに、FunblocksではプロンプトにカスタムVariableを追加できるため、AIアシスタントの実行時により豊かなユーザーインターフェースを表示し、より強力な機能を実現できます。
      ユーザーは自分で作成したプロンプトアプリをプロンプトアプリマーケットに公開し、他のユーザーに提供することもできます。これにより、AIアシスタントに革新的な機能が追加されます。`,
  to_prompts_editor: 'プロンプトアプリを作成',
  settings_memo: 'メモ',
  what_is_memo: 'メモ(Memo)とは?',
  memo_intro: `FunBlocks では、AI が生成したコンテンツや Web ページで選択した素晴らしいコンテンツを、メモとして保存する機能を提供しています。メモを使えば、気になるコンテンツを簡単に保存でき、個性的な知識集を形成できます。これらのメモは、個性化された AI アシスタントの知識ベースとなります。FunBlocks では、保存したメモを手軽に閲覧でき、Notion に匹敵する作成体験もお楽しみいただけます。メモの内容は常に編集可能です。`,
  to_memo: 'メモ(保存済みの即時メモ)を見る',
  settings_shared_aigc: 'AIGC コミュニティ',
  share_aigc_intro: `AI が素晴らしいコンテンツを生成した場合、それを友人と共有したり、FunBlocks の AIGC コミュニティに投稿したりできます。優れた AIGC の共有には報酬として、閲覧ごとに 10 AI コインが付与されます。`,

  close_ai_modal_confirm: 'AI アシスタントを閉じる際の確認不要',
  never_show: '今後確認を求めず、そのまま閉じる',
  settings_more: 'その他の設定',

  disable_confirm: '確認を無効化',
  disabled_pages: '{widget} を無効化したウェブサイトのリスト',
  disable_this_visit: '今回の訪問では無効化',
  disable_this_page: 'このウェブサイトで無効化',
  disable_globally: 'すべてのウェブサイトで無効化',
  view_all: 'すべて表示',
  can_reopen: '設定で再度有効化できます',

  err_video_no_subtitles: '字幕が提供されていないビデオです',
  err_failed_get_subtitles: '字幕の取得に失敗しました。再試行してください。',
  err_unknown_video: '不明なビデオです',

  add_prompt: 'アプリを追加',
  developer: '開発者',
  description: '説明',
  task_content: 'プロンプト変数の出所',
  task_content_from_selected: 'ドキュメントの選択範囲',
  task_content_from_input: 'Single Text Field Input', // 直訳
  task_content_from_form: 'カスタムフォーム入力',
  task_content_form: 'カスタムフォーム',
  prompt_template: 'AI プロンプト',
  prompt_title: 'タイトル',
  prompt_desc_placeholder: 'AI アプリの説明をご記入ください(できること等)...',
  prompt_template_desc:
    'AI へのプロンプトです。{変数} を含める必要があり、AI アシスタントが実行時にこれらを {content_source} に置き換えます。',
  prompt_template_fixed_desc: 'AI へのプロンプト',
  prompt_template_placeholder: `プロンプト内容をご記入ください。例: クリエイティブライターを演じてもらいます。与えるのは一つの段落で、あなたの役割はその意図に従って続きを書くことです。元の文体と調子を維持しつつ、想像力と一貫性を注入して、物語に深みと細部を加え、読者の興味を引いてください。 次の段落が与えられます: {{var_0}}。それではお書きください。`,
  prompt_template_fixed_placeholder:
    'プロンプト内容をご記入ください。例: お笑いトーク役を演じてください。面白い冗談を言ってくれますか?',
  prompt_no_text_err: `プロンプトに {変数} が含まれていることをご確認ください。`,
  prompt_content_placeholder: 'AI に処理してもらう主題やコンテンツ',
  prompt_content_label: '主題',
  prompt_context: '指示の文脈',
  prompt_context_desc: 'AI アシスタントに指示を実行する際の関連コンテンツ',
  context_no_doc_choosen: '関連するページを選択してください',
  context_doc_no_content:
    '選択したページに読み取り可能なコンテンツがありません',
  context: '関連資料',
  current_doc: '現在のページ',
  choose_a_doc: 'ページを選択',
  none: 'なし',
  settings_space_menu: 'スペースの設定',
  settings_space_title: 'スペースの設定',
  settings_members_menu: 'メンバー管理',
  settings_labs_menu: 'ラボ',
  settings_db_capability_switch: 'マルチビューデータベース機能',
  service_subscribe: 'メンバーシップをアップグレード',
  service_subscribe_title: 'メンバーシップをアップグレード',
  service_product_name: '{service} {level} メンバー',
  service_market: 'AI コインを購入',
  price: '価格',
  num: '数量',
  name: '名前',

  pin: 'ピン留め',
  pinned: 'ピン留め済み',
  visibility: '表示範囲',
  visibility_private: '自分のみ(プライベート)',
  visibility_public: '全員(公開)',
  visibility_workspace: '同僚(同じワークスペース)',
  prompts_mine: '自分のアプリ',
  prompts_mine_desc:
    'ここで独自の AI アシスタントアプリを作成できます。または、公開アプリから好みのものを選択することもできます。',
  prompts_developed: '自分が開発したアプリ',
  prompts_public: '公開アプリ',
  prompts_workspace: 'ワークスペースアプリ',
  prompts_pinned: 'ピン留めしたアプリ',
  prompts_pinned_desc:
    'お気に入りの公開またはシェアされたアプリをピン留めしておくと、AI アシスタントのメニューに表示され、いつでも実行できるようになります。',
  prompts_validate: '審査待ちアプリ',
  prompts_drafter: '組み込みの執筆系アプリ',
  prompt_qualified: '合格',
  prompt_not_qualified: '不合格',
  prompt_no_arg: 'ユーザー入力を含まない固定プロンプト',
  run_prompt: '実行',
  prompt_language: 'アプリの説明言語',
  all: 'すべて',
  arg_type: 'タイプ',
  arg_type_textline: '入力フィールド(1行)',
  arg_type_text: '入力フィールド(複数行)',
  arg_type_select: 'ドロップダウン選択',
  select_option_free_input: 'オプション以外の値を入力することを許可する',
  no_suitable_option: '適切なオプションがありません',
  user_input_option: '独自のオプション値を入力',
  select_option_placeholder: 'オプション',
  arg_name: '変数名',
  arg_label: 'タイトル',
  arg_hint: 'ヒント',
  arg_required: '必須',
  arg_optional: 'オプション',
  arg_default_value: 'デフォルト値',
  yes: 'はい',
  no: 'いいえ',
  arg_label_placeholder: 'フォーム項目名',
  arg_hint_placeholder:
    'このフォーム項目に入力する適切な内容をユーザーに示します。例を挙げることもできます。',
  arg_default_value_placeholder: 'ユーザーに表示するデフォルト値',
  add_arg: 'フォーム項目を追加',
  clear_arg: 'リセット',
  update: '更新',
  generate_outline: 'AI に概要を生成させる',
  generate_outline_tooltip:
    '最初に AI に概要を生成させておくと、必要に応じて修正した上で本文を生成させることができます。',
  generate_email_reply_outline_tooltip:
    'メールに書かれた要望を要約し、返信のポイントを生成する',
  no_topic: '執筆する主題を教えてください',
  prompt_blocked_safety:
    'タスクのプロンプトに危険または不適切な内容が含まれていました',
  unknown_failure: '不明なエラーが発生しました',
  summarize_web_page: 'ウェブページの要約',
  summarize_video: 'ビデオの要約',
  video_assistant: 'AI ビデオアシスタント',

  extension_not_work_in_funblocks_site:
    'FunBlocks AI ブラウザ拡張機能は、FunBlocks 外のウェブサイト向けに設計されています。FunBlocks サイト内では、FunBlocks AI アシスタントをご利用ください。',

  prompt_unpin_tooltip:
    'アプリのピン留めを解除し、AI アシスタントメニューから削除します',
  prompt_pin_tooltip: 'アプリをピン留めし、AI アシスタントメニューに表示します',
  prompt_clone_tooltip: 'アプリをコピー/クローンします',
  prompt_edit_tooltip: 'アプリを編集します',
  prompt_delete_tooltip: 'アプリを削除します',
  prompt_share_tooltip: 'アプリをリンク経由で共有します',
  prompt: 'プロンプト',
  confirm_open_ai_generated_slides:
    'スライドが生成されました。プライベートスペースに保存しています。続けて編集しますか?',

  CRUD: '作成/読取/更新/削除',
  not_provided: '未指定',
  new: '新規',
  clone: 'コピー/クローン',

  checkout_form_title_aiplus: '強力な FunBlocks AI を入手する',
  checkout_form_title_funblocks: '{level} メンバーにアップグレード',
  checkout_total: '合計',

  upgrade_to_vip: 'VIP メンバーにアップグレード',
  purchase_ai: 'AI Premium を購入する',
  cancel_subscription: 'メンバーシップを解約する',
  billed_period: '課金期間',
  trial_vip: '{trial_days} 日間の無料トライアル',
  trial_desc:
    'VIP メンバーを {trial_days} 日間無料でお試しいただけます。この期間は無料です。',

  buyable_plans: '購入可能なプラン',
  privileges: 'メンバー特典',
  privileged_feature: 'この機能はメンバー向けです',
  current_plan: '現在のプラン',
  plan_expire_at: '有効期限: {date}',
  last_plan_expired:
    'あなたの {plan} メンバーシップは {date} に期限切れとなりました',
  buy: '今すぐ購入',
  paid_to_check: '支払い済みの場合はボタンをクリック',
  subscribe_status_updated: 'メンバーシップステータスが更新されました',
  aicoins_status_updated: 'AI コイン残高が更新されました',
  goto_vip_buy_page: 'メンバーシップ加入/AI コイン購入',
  aicoin: 'AI コイン',
  exceeded_quota: '現在のメンバーシップでの上限に達しています',
  promote_trial: '無料トライアル',

  feature_for_members:
    'この機能を利用するには、FunBlocks のサービスを利用する必要があります。まずは FunBlocks アカウントを登録またはログインしてください。',
  confirm_logon: 'FunBlocks アカウントの登録またはログインは済んでいますか?',
  unlogin_user: 'ゲストモード',
  login_signin_form_info: 'FunBlocks アカウントにログイン',
  login_signin_vcode_form_info: 'FunBlocks アカウントを作成またはログイン',
  login_resetpswd_form_info: '認証コードを入力してパスワードをリセット',
  login_signup_form_info: 'FunBlocks アカウントを登録',
  forgotpswd: 'パスワードを忘れた場合',
  hadaccount: 'アカウントをお持ちの方',
  login: 'ログイン',
  vcode_login: 'メールアドレスの認証コードでログイン',
  password_login: 'パスワードでログイン',
  logout: 'ログアウト',
  signup: '登録',
  resetpswd: 'パスワードのリセット',
  register_now: '今すぐ登録',
  signup_info:
    'FunBlocks アカウントをお持ちでない方は、1 分で登録が完了します。FunBlocks の AI 執筆やラーニングをお楽しみください。',
  getvcode: '認証コードを取得',
  verification_code: '認証コード',
  vcode_err:
    '間違った認証コードです。入力し直すか、画像をクリックして別の認証コードを表示してください。',
  phone: '電話番号',
  email: 'メールアドレス',
  phone_or_email: '電話番号またはメールアドレス',
  nickname: 'ニックネーム',
  captcha: '画像認証',
  password: 'パスワード',
  connected_account: 'FunBlocks アカウントに関連付けられています',

  confirm_delete_item: '削除すると復元できません。本当に削除しますか?',

  month: '月',
  week: '週',
  day: '日',
  quarter: '四半期',
  year: '年',

  account_not_activated:
    'アカウントはまだ有効化されていません。有効化メールが{email}宛に送信されました。メールを確認し、リンクをクリックしてアカウントを有効化してください。',
  no_activation_email:
    '有効化メールが届かない場合は、右側のボタンをクリックして有効化メールを再送信できます',
  still_no_activation_email:
    '有効化メールが届かない場合は、右側のボタンをクリックして他の登録方法を試してください',
  resend_activation_email: '有効化メールを再送信する',
  try_another_way: '他の登録方法を試す',

  already_activated: '既に有効化済みの場合はボタンをクリックしてください',
  delete: '削除する',
  done: '完了',
  nothing_in_trashbin: 'ゴミ箱は空です',
  delete_all: 'ゴミ箱を空にする',
  restore: '復元する',

  confirm_title: '確認してください',
  confirm_delete_content: '本当に削除しますか?',
  confirm_delete_doc:
    '本当に削除しますか?ゴミ箱から削除したコンテンツは復元できません。',
  confirm: '確認',
  cancel: 'キャンセル',
  release_to_delete: 'このエリアで離すと削除されます',
  remove: '削除する',

  title_input:
    'ページのタイトル/トピックを上部に入力し、AIが執筆できるようにしてください',
  drag: 'ドラッグ',
  to_move: '移動可能な場所',

  click: 'クリック',
  to_open_menu: 'メニューを開く',
  member: 'メンバー',

  user: 'ユーザー',

  role: '役割',

  admin: '管理者',

  invite: '招待する',

  add_user: 'メンバーを追加する',

  invite_user: 'チームメイトを招待する',

  workspace_name: 'ワークスペース名',

  to_read: '未読',

  read: '既読',

  note: 'メモ',

  copy: 'コピー',

  add_ril_tooltip:
    'サードパーティアプリまたはWebサイトからコピーしたリンクを追加する',

  ril_tooltip: 'サードパーティアプリまたはWebサイトから保存した記事',

  accept: '承認する',

  reject: '拒否する',

  cmd_trigger: '欲しいものです',

  cmd_ai_optimize: '改善する',

  cmd_ai_continue: '続ける...',

  cmd_ai_assistant: 'AIアシスタント',

  cmd_trigger_desc: '文字を入力する',

  cmd_ai_optimize_desc: '改善する',

  cmd_ai_continue_desc: '続けて書く...',

  cmd_ai_assistant_desc: 'AIアシスタントに修正または執筆させる...',

  missing_required_data: '必須項目を入力してください',

  missing_one_data: '少なくとも1つの項目を入力してください',

  text: 'テキスト',

  askAI:
    'AIアシスタントに内容を尋ねるか、以下のオプションから関連するキーワードを選択してください...',

  askAI_next: 'AIアシスタントに次の作業を指示してください...',

  askAI_doing: 'AIアシスタントが執筆中',

  askAI_directly_tooltip:
    'ボタンをクリックして、入力内容をそのままAIアシスタントに送るか、以下のメニューから操作を選択してください',

  draft_placeholder:
    'トピックと具体的な要件(文字数、要点など)を入力してください...',

  fill_in_selected: '選択内容を入力する',

  sendAI: 'AIに送信する',

  reset_input: 'リセット',

  draft_artile: '記事を書く',

  topic_label: 'トピック:',

  outline_prompt:
    '{article}を書く予定で、トピックは{topic}、要点は{key_points}です。明確なアウトラインを作成してください',

  draft_more_type: '記事のタイプ',

  selection_text: '選択したテキスト:',

  ai_response: 'AIアシスタント:',

  replace_selection: '選択範囲を置き換える',

  replace_title: 'タイトルを置き換える',

  insert_title: 'タイトルを挿入する',

  insert_below: '生成内容を下に挿入する',

  save_to_memo: 'インスタントメモに保存する',

  saved_to_memo: 'FunBlocksインスタントメモに保存されました',

  copy_and_close: 'コピーして閉じる',

  continue_writing: '続けて書く...',

  make_longer: 'もっと長く書く',

  try_again: 'もう一度試す',

  discard: '破棄する',

  confirm_close_ai:
    'AIアシスタントを閉じますか?保存していない生成コンテンツは破棄されます。',

  confirm_discard_ai_content:
    '前の手順に戻りますか?保存していない生成コンテンツは破棄されます。',

  confirm_no_wait:
    'AIアシスタントは作業中ですが、もう少し待つ必要はありませんか?',

  text_too_long: '選択したテキストが長すぎます',

  no_text: 'AIアシスタントに提供する内容がありません',

  ai_timeout: 'AIアシスタントが長時間応答がありません',

  should_select_item: '次の手順を下のメニューから選択してください',

  should_text_or_select_item:
    'AIにコマンドを入力するか、下のメニューから次のステップを選択してください',

  ai_response_warning:
    'AIアシスタントの返信は必ずしも正しくない可能性があるので、使用前に正確性を確認してください',

  ai_reply: 'AI返信',

  ai_read: 'AIが読む',

  like_this_extension:
    'サービスが気に入った場合は、ご利用の感想と5つ星の評価をお願いします。より多くの人に知ってもらえます!',

  go_rating: 'レビューを書く',

  feedback_here: '製品の提案やバグ報告はこちら',

  generate_lang: '生成言語の設定。デフォルトはプラグインの設定言語です',

  explain: '説明',

  translate: '翻訳',

  optimize: '改善',

  continue: '続ける...',

  other: 'その他',

  share_aigc_reward_tooltip:
    'AI作品を共有すると、閲覧ごとに10 AIコインが付与されます',

  share_aigc: 'AIJCを友達やコミュニティと共有する',

  share_to: 'も同時に共有する',

  aigc_community: 'FunBlocks AIJCコミュニティ',

  your_ai_queries: 'あなたのAIアクセス',

  seems_not_enough: '足りない？',

  get_more: 'もっと取得!',

  ai_token: 'AIトークン',

  ai_token_desc: '1 AIコインで1回のAIタスクリクエストに対応できます',

  your_ai_token_balance: 'あなたのAIコイン残高: ',

  ai_tokens_income: 'AIコイン入金記録',

  show_latest_records: '最新の{count}件のみ表示',

  date: '日付',

  count: '件数',

  income_item: '入金項目',

  new_user_trial: '新規ユーザー無料分',

  invited_by_friend: '招待された報酬',

  invite_friend: 'ユーザー招待報酬',

  shared_aigc_visited: 'AIGC共有閲覧報酬',

  payment: '購入',

  wanna_more_ai_tokens: 'もっとAIコインが必要な場合:',

  hide_move_zoom_icon:
    'AIアシスタントウィンドウで「移動」と「ウィンドウサイズ変更」アイコンを非表示にする',

  to_resize: 'サイズ変更可能',
}
