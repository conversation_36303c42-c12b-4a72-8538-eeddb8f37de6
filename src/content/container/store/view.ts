import { EventName } from '@/common/event-name'
import { MessagePayload } from '@/common/types'
import { useCallback, useEffect, useRef, useState } from 'react'
import { createContainer } from 'unstated-next'
import browser from 'webextension-polyfill'
import { useInstruction } from './instruction'
import { useSelectionManager } from './selection'
import { useDispatch, useSelector } from 'react-redux'
import { getVideoSubtitles } from '@/common/actions/ticketAction'
import { getSetting, useSettings } from '@/common/store/settings'
import {
  getDrafterInfo,
  getTargetEditor,
  getTextIgnoringContentEditableFalse,
  isFunBlocks,
  isGmail,
  isLinkedIn,
  isReddit,
} from '@/content/utils/content-dom'
import { useIntl } from 'react-intl'

export const action_bar_widget_id = 'action_bar_widget_id'

const { useContainer: useView, Provider: ViewProvider } = createContainer(
  () => {
    const [viewStatus, setViewStatus] = useState<
      'icon' | 'input' | 'result' | 'message' | 'none'
    >('none')
    const viewStatusRef = useRef<string>()
    viewStatusRef.current = viewStatus
    const selection = useSelectionManager()
    const { setInstruction } = useInstruction()
    const disposeListRef = useRef<(() => void)[]>([])
    const [objType, setObjType] = useState('markdown')
    const [action, setAction] = useState()
    const [sub_item_action, set_sub_item_action] = useState()
    const [drafter, setDrafter] = useState()
    const [message, setMessage] = useState()
    const dispatch = useDispatch()
    const { refresh, settings } = useSettings()

    const closeWidgetModalState =
      useSelector((state) => state.uiState.closeWidgetDialog) || {}

    const intl = useIntl()
    // const params = new Proxy(new URLSearchParams(window.location.search), {
    //   get: (searchParams, prop) => searchParams.get(prop) || '',
    // })
    const [params, setParams] = useState(
      new Proxy(new URLSearchParams(window.location.search), {
        get: (searchParams, prop) => searchParams.get(prop) || '',
      })
    )

    const disposeAll = useCallback(() => {
      disposeListRef.current.forEach((c) => c())
      disposeListRef.current = []
    }, [])

    const goToInputPage = useCallback(
      ({
        objType,
        action,
        sub_item_action,
        drafter,
        selected_content_for_ai,
      }) => {
        disposeAll()
        selection.rePositionToCenter()
        setViewStatus('input')
        setObjType(objType)
        setAction(action)
        set_sub_item_action(sub_item_action)
        setDrafter(drafter)

        selection.setSelectedContentForAI(
          selected_content_for_ai || selection.getSelectedContent()
        )
        selection.setLock(true)
      },
      []
    )

    const goToResult = useCallback(() => {
      setViewStatus('result')
      disposeAll()
    }, [])

    const goToIcon = useCallback(() => {
      selection.setLock(true)
      setViewStatus('icon')
      document.addEventListener('click', hide)
      disposeListRef.current.push(() => {
        document.removeEventListener('click', hide)
      })
    }, [])

    const openImageAssistant = useCallback((image) => {
      selection.setSelectedContent({
        type: 'image',
        image,
      })
      goToInputPage({ objType: 'image' })
    }, [])

    const openWriterAssistant = useCallback((withoutSelection) => {
      if (withoutSelection) {
        selection.setSelectedContent(null)
      }
      goToInputPage({ objType: 'markdown' })
    }, [])

    const openWriterAssistantForEditor = useCallback(
      async (targetEditor, trigger, type) => {
        selection.select_from_start(targetEditor, trigger)
        const existedText = selection.getSelectedContent()?.text?.trim()

        let drafterInfo
        if (
          type == 'composer' ||
          !existedText ||
          /^@[^\s.,!?:;'"()\[\]{}<>\\\/|`~@#$%^&*+=-]+$/.test(
            existedText.trim()
          )
        ) {
          drafterInfo = await getDrafterInfo(targetEditor, selection)

          if (drafterInfo?.main_content) {
            selection.setSelectedContent(
              typeof drafterInfo.main_content === 'object'
                ? {
                    ...drafterInfo.main_content,
                    isEditable: true,
                    drafter: drafterInfo.drafter,
                  }
                : {
                    text: drafterInfo.main_content,
                    isEditable: true,
                    drafter: drafterInfo.drafter,
                  }
            )
          }
        }

        const selected_content = selection.getSelectedContent()
        let topic, keypoints

        if (targetEditor?.innerText && targetEditor?.textContent) {
          if (isGmail) {
            if (getTextIgnoringContentEditableFalse(targetEditor)) {
              keypoints = targetEditor?.textContent
            }
          } else {
            keypoints = targetEditor?.textContent
          }
        } else if (targetEditor?.tagName === 'TEXTAREA') {
          keypoints = targetEditor?.value
        }

        if (
          keypoints?.trim() === settings?.trigger ||
          (keypoints?.trim() &&
            /^@[^\s.,!?:;'"()\[\]{}<>\\\/|`~@#$%^&*+=-]+$/.test(
              keypoints.trim()
            ))
        ) {
          keypoints = undefined
        }

        if (isLinkedIn) {
          if (window.location.pathname.includes('article')) {
            topic = document.getElementById(
              'article-editor-headline__textarea'
            )?.value
          }
        } else if (isReddit) {
          const postComposer = document.querySelector('r-post-composer-form')
          if (postComposer) {
            topic = postComposer
              .querySelector('[name="title"]')
              ?.shadowRoot?.getElementById('innerTextArea')?.value
          }
        }

        if (topic || keypoints) {
          selection.setSelectedContent({
            ...selected_content,
            isEditable: true,
            topic,
            keypoints,
          })
        }
        const selected_content_for_ai = { ...selection.getSelectedContent() }

        // setTimeout(() => {
        goToInputPage({
          objType:
            !existedText && drafterInfo?.main_content ? 'ril' : 'markdown',
          drafter: drafterInfo?.drafter,
          action: drafterInfo?.action,
          selected_content_for_ai,
        })
        // }
        // , 300)
      },
      [settings?.trigger]
    )

    function retrieveTranscript(successCallback, failureCallback) {
      const videoId = new URLSearchParams(window.location.search).get('v')
      if (!videoId) {
        return failureCallback(new Error('Video ID not found in URL'))
      }

      const YT_INITIAL_PLAYER_RESPONSE_RE =
        /ytInitialPlayerResponse\s*=\s*({.+?})\s*;\s*(?:var\s+(?:meta|head)|<\/script|\n)/
      let player = window.ytInitialPlayerResponse

      if (!player || videoId !== player.videoDetails?.videoId) {
        fetch('https://www.youtube.com/watch?v=' + videoId)
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }
            return response.text()
          })
          .then((body) => {
            const playerResponse = body.match(YT_INITIAL_PLAYER_RESPONSE_RE)
            if (!playerResponse) {
              throw new Error('Unable to parse playerResponse')
            }

            player = JSON.parse(playerResponse[1])
            const metadata = {
              title: player.videoDetails.title,
              duration: player.videoDetails.lengthSeconds,
              author: player.videoDetails.author,
              views: player.videoDetails.viewCount,
            }

            const tracks =
              player.captions?.playerCaptionsTracklistRenderer?.captionTracks
            if (!tracks || tracks.length === 0) {
              throw new Error('No caption tracks found')
            }

            tracks.sort(compareTracks)

            return fetch(tracks[0].baseUrl + '&fmt=json3')
          })
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }
            return response.json()
          })
          .then((transcript) => {
            const parsedTranscript = transcript.events
              .filter((x) => x.segs)
              .map((x) =>
                x.segs
                  .map((y) => y.utf8)
                  .join(' ')
                  .replace(/[\u200B-\u200D\uFEFF]/g, '')
                  .replace(/\s+/g, ' ')
              )

            // console.log('EXTRACTED_TRANSCRIPT', parsedTranscript)
            successCallback(parsedTranscript)
          })
          .catch((error) => {
            console.error('Error in retrieveTranscript:', error)
            failureCallback(error)
          })
      } else {
        failureCallback(new Error('Player data not available'))
      }
    }

    function compareTracks(track1, track2) {
      const langCode1 = track1.languageCode
      const langCode2 = track2.languageCode

      if (langCode1 === 'en' && langCode2 !== 'en') {
        return -1 // English comes first
      } else if (langCode1 !== 'en' && langCode2 === 'en') {
        return 1 // English comes first
      } else if (track1.kind !== 'asr' && track2.kind === 'asr') {
        return -1 // Non-ASR comes first
      } else if (track1.kind === 'asr' && track2.kind !== 'asr') {
        return 1 // Non-ASR comes first
      }

      return 0 // Preserve order if both have same priority
    }

    const openReaderAssistant = useCallback(
      (setLoadingDone) => {
        if (
          window.location.hostname.toLowerCase().includes('youtube.com') &&
          params.v
        ) {
          // console.log('will request youtube id.......', params.v)
          retrieveTranscript(
            (subtitles) => {
              // console.log('subtitles...........', subtitles)
              !!setLoadingDone && setLoadingDone()
              if (subtitles && subtitles.length > 0) {
                let text = subtitles
                  // .map((subtitle) => subtitle.text)
                  .join(' ')
                selection.setSelectedContent({
                  type: 'video',
                  text,
                  subtitles,
                  title: document.title,
                })
              } else {
                selection.selectWholePage()
              }

              goToInputPage({ objType: 'ril' })
            },
            () => {
              !!setLoadingDone && setLoadingDone()
              selection.selectWholePage()
              goToInputPage({ objType: 'ril' })
            }
          )
        } else {
          !!setLoadingDone && setLoadingDone()
          selection.selectWholePage()
          goToInputPage({ objType: 'ril' })
          return
        }
      },
      [params?.v, window.location.hostname]
    )

    const openAssistantForSummary = useCallback(
      (setLoadingDone) => {
        if (
          window.location.hostname.toLowerCase().includes('youtube.com') &&
          params.v
        ) {
          !!setLoadingDone && setLoadingDone()

          retrieveTranscript(
            (subtitles) => {
              if (subtitles && subtitles.length > 0) {
                let text = subtitles
                  // .map((subtitle) => subtitle.text)
                  .join(' ')
                selection.setSelectedContent({
                  type: 'video',
                  text,
                  subtitles,
                })
                !!setLoadingDone && setLoadingDone()
                return goToInputPage({ objType: 'ril', action: 'summary' })
              } else {
                !!setLoadingDone && setLoadingDone('err_video_no_subtitles')
                // selection.selectWholePage()
                // return goToInputPage('ril')
              }
            },
            (err) => {
              !!setLoadingDone && setLoadingDone('err_failed_get_subtitles')
            }
          )
        } else {
          return 'err_unknown_video'
        }
      },
      [params?.v, window.location.hostname]
    )

    const hide = useCallback(() => {
      setViewStatus('none')
      setObjType('markdown')
      selection.setLock(false)
      selection.setSelectedContent({ text: '' })
      selection.setGeneratedText('')
      selection.setTriggerEvent(null)
      disposeAll()
    }, [])

    const reset = useCallback(() => {
      setViewStatus('none')
    }, [])

    useEffect(() => {
      let id = null

      selection.onSelectionChange((selectedContent) => {
        if (selectedContent?.text && viewStatusRef.current === 'none') {
          goToIcon()

          clearTimeout(id)
          getSetting().then((settings) => {
            if (settings.balloon_auto_hide == 'true') {
              id = setTimeout(() => {
                if (viewStatusRef.current === 'icon') {
                  hide()
                }
              }, 5000)
            }
          })
        }

        // console.log('view status......', selectedContent?.text, viewStatusRef.current)
        if (!selectedContent?.text && viewStatusRef.current == 'icon') {
          hide()
        }
      })

      return () => {
        disposeAll()
        clearTimeout(id)
      }
    }, [])

    useEffect(() => {
      const listener = (message) => {
        if (
          isFunBlocks &&
          [
            EventName.launchFunBlocks,
            EventName.launchFunBlocksWriter,
            EventName.launchFunBlocksReader,
          ].includes(message.type)
        ) {
          setMessage('extension_not_work_in_funblocks_site')
          setViewStatus('message')
        } else if (message.type === EventName.launchFunBlocks) {
          openWriterAssistant()
        } else if (message.type === EventName.launchFunBlocksWriter) {
          openWriterAssistant()
        } else if (message.type === EventName.launchFunBlocksReader) {
          openReaderAssistant()
        } else if (message.type === EventName.launchFunBlocksResultPanel) {
          setInstruction(message.data?.instruction)
          goToResult()
          return
        } else if (
          message.type === EventName.onTabUpdated &&
          message.info?.status == 'loading'
        ) {
          const p = new Proxy(new URLSearchParams(window.location.search), {
            get: (searchParams, prop) => searchParams.get(prop) || '',
          })
          // if(p?.v) {
          setParams(p)
          // }
        } else if (message.type === EventName.onTabActived) {
          refresh()
        } else if (message.type == 'screenshot') {
          console.log(message)
          const a = document.createElement('a')
          a.setAttribute('download', 'screenshot.png')
          a.setAttribute('href', message.info)
          a.click()
        }
      }

      browser.runtime.onMessage.addListener(listener)

      return () => browser.runtime.onMessage.removeListener(listener)
    }, [openReaderAssistant, openWriterAssistant])

    return {
      viewStatus,
      goToInputPage,
      goToResult,
      goToIcon,
      openImageAssistant,
      openWriterAssistant,
      openReaderAssistant,
      openAssistantForSummary,
      openWriterAssistantForEditor,
      retrieveTranscript,
      hide,
      reset,
      message,
      objType,
      action,
      sub_item_action,
      action_bar_widget_id,
      drafter,
    }
  }
)

export { useView, ViewProvider }
