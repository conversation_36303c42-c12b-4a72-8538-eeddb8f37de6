import { Logo } from "@/components/icon";
import { findClosestNodeWithSelector, getContentDom, isGitHub } from "@/content/utils/content-dom";
import { useEffect, useState } from "react";
import { ThreeDots } from "react-loader-spinner";
import { createContainer } from "unstated-next";

const { useContainer: useCodesExplain, Provider: CodesExpainProvider } = createContainer(
    () => {
        const explainElementId = 'funblocks-explain-widget';

        const CodesExplainWidget = ({ label, loading, codes }) => {
            const [hovered, setHovered] = useState();

            return <div
                className="fill-available"
                style={{
                    display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end',
                    paddingLeft: 6, paddingRight: 6,
                }}
            >
                <div
                    style={{
                        display: 'flex', flexDirection: 'row', alignItems: 'center',
                        backgroundColor: !loading && hovered?.id == 'codes_explain' ? 'azure' : 'white',
                        color: 'dodgerblue',
                        cursor: 'pointer', whiteSpace: 'nowrap',
                        border: '1px solid dodgerblue',
                        borderRadius: '5px',
                        fontSize: 12,
                        width: 'fit-content',
                        padding: 3,
                        paddingLeft: 5,
                        paddingRight: 7,
                        columnGap: 3,
                        fontFamily: 'DM Sans',
                        fontWeight: 400
                    }}
                    tabIndex={0}

                    onMouseEnter={() => {
                        setHovered({ id: 'codes_explain' })
                    }}
                    onMouseLeave={() => {
                        setHovered(null)
                    }}
                    onClick={(event) => {
                        let codeEle, codes;
                        if (isGitHub) {
                            codeEle = document.querySelector('textarea#read-only-cursor-text-area')
                            codes = codeEle.value
                        } else {
                            codeEle = findClosestNodeWithSelector(event.currentTarget, 'code')?.querySelector('code')
                            codes = codeEle?.innerText
                        }

                        window.postMessage({ type: 'launch_codes_explain', data: { text: codes } }, '*');
                    }}
                >
                    {/* <img width={24} height={24} src={imagePath} draggable={false} /> */}
                    <Logo size={15} />

                    {label}

                </div>
            </div>
        }

        return {
            explainElementId,
            CodesExplainWidget,
            // getHookDomForSummary,
        }
    }
)

export { useCodesExplain, CodesExpainProvider }