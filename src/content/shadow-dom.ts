import { getDomain } from './utils/content-dom'

const tag = 'funblocks-container'
const conatinerId = 'funblocks-container-id'

// Create a class for the element
class FunBlocksContainer extends HTMLElement {
  constructor() {
    // Always call super first in constructor
    super()

    // Create a shadow root
    const shadow = this.attachShadow({ mode: 'open' })

    const container = document.createElement('div')
    container.id = conatinerId
    if (getDomain() !== 'reddit') {
      container.setAttribute('style', 'font-size:16px;')
    } else {
      container.style.cssText = `
        all: initial;
        contain: content;
        isolation: isolate;
        font-size: 16px;
        pointer-events: auto;
        z-index: 2147483647;
      `
    }

    shadow.appendChild(container)

    /**
     * Prevent bubble, cause the host website might listen them to make thing unexpected
     * For example notion, it listens on keyup event to delete content
     * https://developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent
     * https://developer.mozilla.org/en-US/docs/Web/API/InputEvent/inputType
     */
    ;[
      'click',
      'keydown',
      'keypress',
      'keyup',
      'copy',
      'paste',
      'mouseup',
    ].forEach((eventName) => {
      shadow.addEventListener(eventName, (e) => {
        e.stopPropagation()
      })
    })
  }
}

// Define the new element
customElements.define(tag, FunBlocksContainer)

export { tag, conatinerId }
