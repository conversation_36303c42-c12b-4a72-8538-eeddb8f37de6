import { DashiconsAdminGeneric } from '@/components/icon'
import './index.css'
import browser from 'webextension-polyfill'
import { useIntl } from 'react-intl'
import { EventName } from '@/common/event-name'
import { Magic } from '@styled-icons/bootstrap/Magic'
import { BookReader } from '@styled-icons/boxicons-regular/BookReader'
import { Settings } from '@styled-icons/material'

export const Menu: React.FC = () => {
  const intl = useIntl()
  const menu = [
    {
      icon: <BookReader size={18} />,
      label: 'FunBlocks AI',
      onClick: () => {
        chrome.tabs.query(
          { active: true, currentWindow: true },
          function (tabs) {
            browser.tabs.sendMessage(tabs[0].id, {
              type: EventName.launchFunBlocksReader,
            })
          }
        )
      },
    },
    // {
    //   icon: <Magic size={18} />,
    //   label: intl.formatMessage({ id: 'assistant_writer' }),
    //   onClick: () => {
    //     chrome.tabs.query(
    //       { active: true, currentWindow: true },
    //       function (tabs) {
    //         browser.tabs.sendMessage(tabs[0].id, {
    //           type: EventName.launchFunBlocksWriter,
    //         })
    //       }
    //     )
    //   },
    // },
    {
      icon: <Settings size={18} />,
      label: intl.formatMessage({ id: 'settings' }),
      onClick: () => {
        const url = browser.runtime.getURL('dist/options/index.html')
        window.open(url)
      },
    },
  ]

  return menu.map((item) => {
    return (
      <div
        className="hoverStand"
        style={{
          paddingLeft: 12,
          fontSize: 16,
        }}
        onClick={() => {
          item.onClick()
          window.close()
        }}
      >
        <div
          style={{
            color: 'dodgerblue',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: 6,
          }}
        >
          {item.icon}
        </div>
        {item.label}
      </div>
    )
  })
}
