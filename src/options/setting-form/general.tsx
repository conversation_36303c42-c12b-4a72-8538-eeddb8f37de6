import { useIntl } from 'react-intl'
import { Selector } from '@/common/selector'
import { useSettings } from '@/common/store/settings'
import { DialogContent, Dialog, Button, Avatar } from '@mui/material'
import { blueGrey, grey, lightBlue } from '@mui/material/colors'

import browser from 'webextension-polyfill'
import { useDispatch, useSelector } from 'react-redux'
import { useEffect, useState } from 'react'
import {
  getAICoinBalanceAndTrans,
  getAvailableProducts,
  getLngList,
  getServingProducts,
} from '@/common/actions/ticketAction'
import { formatDate } from '@/common/utils'
import { funblocks_domain } from '@/common/serverAPIUtil'
import { SETTINGS_DIALOG } from '@/common/constants/actionTypes'

const capitalizeFirstLetter = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

const General = () => {
  const intl = useIntl()
  const dispatch = useDispatch()
  const { settings = {}, setSettings } = useSettings()
  const loginUser = useSelector((state) => state.loginIn.user)
  const serving_products =
    useSelector((state) => state.uiState.serving_products) || []
  const available_products = useSelector(
    (state) => state.uiState.available_products
  )
  const ai_tokens_trans = useSelector(
    (state) => state.uiState.ai_token_balance_trans
  )
  const [ai_privilege, set_ai_privilege] = useState()

  useEffect(() => {
    dispatch(getLngList({ locale: settings.lang }))
  }, [])

  useEffect(() => {
    if (!loginUser?._id) {
      return
    }

    dispatch(getServingProducts({ services: ['aiplus'] }, null, 'settings'))
    dispatch(getAvailableProducts({ services: ['aiplus'] }, null, 'settings'))
    dispatch(getAICoinBalanceAndTrans({}, null, 'settings'))
  }, [loginUser])

  useEffect(() => {
    if (!available_products?.products?.aiplus) {
      return
    }

    const serving_product = !!serving_products?.length
      ? serving_products[0].name
      : 'free'

    set_ai_privilege(
      available_products.products['aiplus'][serving_product].privileges.askAI
    )
  }, [serving_products, available_products])

  return (
    <div
      className="fill-available"
      style={{
        alignItems: 'center',
        justifyContent: 'flex-start',
        padding: 10,
        paddingBottom: 40,
      }}
    >
      <div style={styles.section_title}>
        {intl.formatMessage({ id: 'connected_account' })}
      </div>
      <div style={styles.section_box}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            padding: 6,
            justifyContent: 'space-between',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <Avatar
              sx={{
                bgcolor: grey[400],
                width: 36,
                height: 36,
                marginRight: '6px',
              }}
              variant="circle"
            >
              <span style={{ fontSize: 16 }}>
                {loginUser.nickname ? loginUser.nickname[0].toUpperCase() : 'G'}
              </span>
            </Avatar>
            {loginUser._id && (
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  fontSize: 14,
                }}
              >
                <span>{loginUser.nickname}</span>
                <span style={{ color: 'gray' }}>{loginUser.username}</span>
              </div>
            )}
            {!loginUser._id && (
              <div
                style={{ padding: 4, display: 'flex', flexDirection: 'column' }}
              >
                {intl.formatMessage({ id: 'unlogin_user' })}
                <div
                  style={{
                    cursor: 'pointer',
                    color: 'dodgerblue',
                    fontSize: 15,
                  }}
                  onClick={() => {
                    window.open(
                      `https://app.${funblocks_domain}/#/login?source=extension`
                    )
                  }}
                >
                  {intl.formatMessage({ id: 'login' }) +
                    '/' +
                    intl.formatMessage({ id: 'signup' })}
                </div>
              </div>
            )}
          </div>

          {loginUser._id && (
            <div>
              <a href="https://app.funblocks.net" target="_blank">
                <div
                  className="p-2 rounded-sm hover:rounded-md bg-gray-50 hover:bg-gray-200 transition-all duration-300 cursor-pointer"
                  style={{ fontSize: 15 }}
                >
                  FunBlocks Web App
                </div>
              </a>
            </div>
          )}
        </div>
      </div>

      <div style={styles.section_title}>
        {intl.formatMessage({ id: 'current_plan' })}
      </div>
      <div style={styles.section_box}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            padding: 6,
            justifyContent: 'space-between',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              fontSize: 16,
              rowGap: 4,
            }}
          >
            <p>
              {intl.formatMessage({ id: 'your_ai_queries' })} {ai_privilege}
            </p>
            <p>
              {intl.formatMessage({ id: 'seems_not_enough' })}
              <a
                href={`http://app.${funblocks_domain}/#/aiplans`}
                target="_blank"
                style={{ textDecoration: 'none', color: 'dodgerblue' }}
              >
                &nbsp;{intl.formatMessage({ id: 'get_more' })}
              </a>
            </p>
          </div>

          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              fontSize: 20,
              alignItems: 'flex-start',
              justifyItems: 'flex-start',
            }}
          >
            FunBlocks{' '}
            {capitalizeFirstLetter(
              (serving_products?.length > 0 && serving_products[0].name) ||
                'Free'
            )}
          </div>
        </div>
      </div>

      <div style={styles.section_title}>
        {intl.formatMessage({ id: 'ai_token' })}
      </div>
      <div style={styles.section_box}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            fontSize: 16,
            rowGap: 4,
            padding: 6,
          }}
        >
          <p>
            {intl.formatMessage({ id: 'your_ai_token_balance' })}{' '}
            {ai_tokens_trans?.balance?.coins || 0}&nbsp;
            <span style={{ fontSize: 14, color: 'GrayText' }}>
              {intl.formatMessage({ id: 'ai_token_desc' })}
            </span>
          </p>
          {!!ai_tokens_trans?.trans?.length && (
            <div>
              <p>
                {intl.formatMessage({ id: 'ai_tokens_income' })}&nbsp;
                <span style={{ fontSize: 14, color: 'GrayText' }}>
                  {intl.formatMessage(
                    { id: 'show_latest_records' },
                    { count: 10 }
                  )}
                </span>
              </p>
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  padding: 20,
                  backgroundColor: '#f3f3f3',
                  borderRadius: 10,
                  marginTop: 10,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    fontWeight: 500,
                  }}
                >
                  <div style={{ flex: 1 }}>
                    {intl.formatMessage({ id: 'income_item' })}
                  </div>
                  <div style={{ flex: 1 }}>
                    {intl.formatMessage({ id: 'count' })}
                  </div>
                  <div style={{ flex: 1 }}>
                    {intl.formatMessage({ id: 'date' })}
                  </div>
                </div>

                {ai_tokens_trans.trans.map((item, index) => {
                  return (
                    <>
                      <div
                        style={{
                          height: 1,
                          backgroundColor: '#ccc',
                          marginTop: 8,
                        }}
                      />
                      <div
                        key={index + ''}
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center',
                          fontSize: 14,
                          paddingTop: 8,
                          color: '#444',
                        }}
                      >
                        <div style={{ flex: 1 }}>
                          {intl.formatMessage({ id: item.activity })}
                        </div>
                        <div style={{ flex: 1 }}>{item.coins}</div>
                        <div style={{ flex: 1 }}>
                          {formatDate(item.createdAt)}
                        </div>
                      </div>
                    </>
                  )
                })}
              </div>
            </div>
          )}
          <div style={{ paddingTop: 8 }}>
            <span>{intl.formatMessage({ id: 'wanna_more_ai_tokens' })}</span>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                cursor: 'pointer',
                color: 'dodgerblue',
                marginTop: 3,
              }}
              onClick={() => {
                window.open(
                  `https://app.${funblocks_domain}/#/invitation-event`
                )
              }}
            >
              <span>🎁&nbsp;</span>
              {intl.formatMessage({ id: 'invite_friends' })}
            </div>
            <div
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                cursor: 'pointer',
                color: 'dodgerblue',
                marginTop: 3,
              }}
              onClick={() => {
                setSettings({
                  ...settings,
                  toSettingsPage: 'shared_aigc',
                })
              }}
            >
              <span>👩‍💻&nbsp;</span>
              {intl.formatMessage({ id: 'share_aigc' })}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const styles = {
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    paddingTop: '10px',
    columnGap: '10px',
  },

  title: {
    fontWeight: 500,
    fontSize: 16,
    whiteSpace: 'nowrap',
  },

  section_title: {
    fontWeight: '500',
    fontSize: 24,
    whiteSpace: 'nowrap',
    paddingTop: 34,
    paddingBottom: 12,
  },

  section_box: {
    padding: 14,
    boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
    border: '1px solid #e3e3e3',
    borderRadius: 6,
  },

  input: {
    padding: '3px 12px',
    border: '1px solid #ccc',
    borderRadius: 15,
    outline: 'none',
    minWidth: '400px',
    fontSize: 14,
  },
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
    minWidth: 80,
  },
}

export default General
