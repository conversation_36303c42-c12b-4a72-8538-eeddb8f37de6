import * as React from 'react';
import Box from '@mui/material/Box';

import Typography from '@mui/material/Typography';
import Divider from '@mui/material/Divider';
import { useDispatch, useSelector } from 'react-redux';
import { DialogContent, Dialog, Button, Avatar } from '@mui/material';
import { getStateByUser } from '@/common/reducers/listReducer';
import { useIntl } from 'react-intl';
import LlmProvider from './llm-provider';
import Prompts from './prompts';
import { getAppConfig, getLngList, getUserInfo } from '@/common/actions/ticketAction';
import { blueGrey, grey, lightBlue } from '@mui/material/colors';
import { useSettings } from '@/common/store/settings';
import { funblocks_domain } from '@/common/serverAPIUtil';
import Feedbacks from './feedbacks';
import QuickAction from './quick_action';
import QuickCompose from './quick_compose';
import SideBar from './sidebar';
import { Check } from '@styled-icons/material';
import Memo from './memo';
import Workspace from './workspace';
import MoreSettings from './more-settings';
import General from './general';
import SharedAIGC from './shared_aigc';
import { APP_CONFIG } from '@/common/constants/actionTypes';

const drawerWidth = 280;

const PrivilegeItem = ({ text, desc }) => {
    return <div style={{
        display: 'flex',
        flexDirection: 'column',
        rowGap: 6,
        paddingTop: 2,
        paddingBottom: 2,
    }}>
        <div style={{
            display: 'flex',
            flexDirection: 'row',
            columnGap: 6
        }}>
            <Check size={20} color='green' />
            {text}
        </div>
        {
            desc &&
            <span style={{ color: '#999', fontSize: 13, paddingLeft: 28 }}>{desc}</span>
        }

    </div>
}

const Board = () => {
    const dispatch = useDispatch();
    const intl = useIntl();
    const { settings, setSettings } = useSettings();

    const loginUser = useSelector(state => state.loginIn.user);
    const orgs = useSelector(state => getStateByUser(state.org_lists, loginUser));
    const lng = settings.lang;
    const [btn_hovered, set_btn_hovered] = React.useState({});

    React.useEffect(() => {
        dispatch(getUserInfo({}));

        dispatch(getAppConfig({ service: 'extension', locale: lng }, (data) => {
            dispatch({
              type: APP_CONFIG,
              value: data
            })
          }));
    }, [])

    const accountSettings = React.useMemo(() => [{
        id: 'general_settings',
        text: intl.formatMessage({ id: 'settings_general' }),
        page: <General />
    }, {
        id: 'api_settings',
        text: intl.formatMessage({ id: 'settings_api_menu' }),
        title: intl.formatMessage({ id: 'settings_api_title' }),
        page: <LlmProvider />
    }, {
        //     id: 'prompts',
        //     text: intl.formatMessage({ id: 'settings_prompts_menu' }),
        //     page: <Prompts />
        // }, {
        id: 'side_bar',
        text: intl.formatMessage({ id: 'settings_side_bar' }),
        page: <SideBar />
    }, {
        id: 'quick_action',
        text: intl.formatMessage({ id: 'settings_quick_action' }),
        page: <QuickAction />
    }, {
        id: 'quick_compose',
        text: intl.formatMessage({ id: 'settings_quick_compose' }),
        page: <QuickCompose />
    }, {
        id: 'prompts',
        text: intl.formatMessage({ id: 'settings_prompts_menu' }),
        page: <Prompts />
    }, {
        id: 'memo',
        text: intl.formatMessage({ id: 'settings_memo' }),
        page: <Memo />
    }, {
        id: 'space_intro',
        text: intl.formatMessage({ id: 'space_intro' }),
        page: <Workspace />
    }, {
        id: 'shared_aigc',
        text: intl.formatMessage({ id: 'settings_shared_aigc' }),
        page: <SharedAIGC />
    }, {
        id: 'more_settings',
        text: intl.formatMessage({ id: 'settings_more' }),
        page: <MoreSettings />
    }, {
        id: 'feedbacks',
        text: intl.formatMessage({ id: 'feedback' }),
        page: <Feedbacks />
    }], [intl, lng]);


    const [currentPage, setCurrentPage] = React.useState(accountSettings[0]);
    const [itemHovered, setItemHovered] = React.useState();

    React.useEffect(() => {
        if (settings?.toSettingsPage) {
            setCurrentPage(accountSettings.find(item => item.id === settings?.toSettingsPage))
            setSettings({
                ...settings,
                toSettingsPage: '',
            })
        }
    }, [settings?.toSettingsPage])

    const menuItemRenderer = (item, index, list_name) => {
        return (
            <div
                key={item.text}
                onClick={() => setCurrentPage(item)}
                style={{
                    backgroundColor: currentPage && currentPage.text === item.text ? 'lightskyblue' : (itemHovered?.index === index && itemHovered?.list_name == list_name ? '#dedede' : 'transparent'),
                    color: currentPage && currentPage.text === item.text ? 'white' : undefined,
                    padding: 5,
                    paddingBottom: 6,
                    marginBottom: 10,
                    marginTop: 10,
                    marginLeft: 4,
                    marginRight: 4,
                    borderRadius: 15,
                    cursor: 'pointer',
                }}
                onMouseEnter={() => setItemHovered({ index, list_name })}
                onMouseLeave={() => setItemHovered(null)}
            >
                <span
                    style={{ fontSize: '15px', paddingLeft: 6 }}
                >
                    {item.text}
                </span>
            </div>
        );
    }

    return (
        <div style={{ display: 'flex', flexDirection: 'row', padding: 0, height: '100%' }}>
            <div
                style={{
                    width: drawerWidth,
                    padding: 10,
                    // paddingTop: '20px',
                    backgroundColor: '#f0f0f0',

                }}
            >
                {/* <div style={{ color: '#999', fontSize: 13, padding: 4, paddingLeft: 16, fontWeight: 'bold' }}>
                    <span>{intl.formatMessage({ id: 'settings_account' })}</span>
                </div> */}
                {/* <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', padding: 6 }}>
                    <Avatar sx={{ bgcolor: grey[400], width: 36, height: 36, marginRight: '6px' }} variant="circle">
                        <span style={{ fontSize: 16 }}>
                            {loginUser.nickname ? loginUser.nickname[0].toUpperCase() : 'G'}
                        </span>
                    </Avatar>
                    {
                        loginUser._id &&
                        <div style={{ display: 'flex', flexDirection: 'column', fontSize: 14 }}>
                            <span>{loginUser.nickname}</span>
                            <span style={{ color: 'gray' }}>{loginUser.username}</span>
                        </div>
                    }
                    {
                        !loginUser._id &&
                        <div style={{ padding: 4, display: 'flex', flexDirection: 'column' }}>
                            {intl.formatMessage({ id: 'unlogin_user' })}
                            <div style={{ cursor: 'pointer', color: 'dodgerblue', fontSize: 15 }} onClick={() => {
                                window.open(`https://app.${funblocks_domain}/#/login?source=extension`);
                            }}>
                                {intl.formatMessage({ id: 'login' }) + '/' + intl.formatMessage({ id: 'signup' })}
                            </div>
                        </div>
                    }
                </div> */}

                {accountSettings.map((item, index) => menuItemRenderer(item, index, 'account'))}
                {
                    loginUser._id &&
                    <>
                        <div style={{
                            border: '1px solid #ccc',
                            borderRadius: 8,
                            padding: 8,
                            paddingTop: 10,
                            fontSize: 14,
                            marginTop: 30
                        }}>
                            <div style={{
                                fontWeight: 600,
                                marginBottom: 10,
                                marginTop: 4,
                            }}>
                                Get Unlimited Plan for
                            </div>
                            <PrivilegeItem text={"Unlimited queries"} />
                            <PrivilegeItem text={"GPT-4 Access"} />
                            <PrivilegeItem text={"AI assistant everywhere"} />
                            <div
                                style={{
                                    border: '1px solid dodgerblue',
                                    borderRadius: 8,
                                    padding: 4,
                                    marginTop: 10,
                                    marginBottom: 6,
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    cursor: 'pointer',
                                    color: btn_hovered?.btn == 'trial' ? 'white' : undefined,
                                    backgroundColor: btn_hovered?.btn == 'trial' ? 'dodgerblue' : 'paleturquoise',
                                    fontWeight: 600
                                }}

                                onMouseEnter={() => set_btn_hovered({ btn: 'trial' })}
                                onMouseLeave={() => set_btn_hovered({})}
                                onClick={() => {
                                    window.open(`http://app.${funblocks_domain}/#/aiplans`)
                                }}
                            >
                                <span>🎉&nbsp;</span>
                                {intl.formatMessage({ id: 'promote_trial' })}
                            </div>
                        </div>

                        <div style={{
                            border: '1px solid #ccc',
                            borderRadius: 8,
                            padding: 8,
                            paddingTop: 10,
                            fontSize: 14,
                            marginTop: 30
                        }}>
                            Get free AI queires by
                            <div
                                style={{
                                    border: '1px solid #bbb',
                                    borderRadius: 8,
                                    padding: 4,
                                    marginTop: 10,
                                    marginBottom: 6,
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    cursor: 'pointer',
                                    color: btn_hovered?.btn == 'invite_friend' ? 'white' : undefined,
                                    backgroundColor: btn_hovered?.btn == 'invite_friend' ? 'dodgerblue' : undefined,
                                    fontWeight: 600
                                }}

                                onMouseEnter={() => set_btn_hovered({ btn: 'invite_friend' })}
                                onMouseLeave={() => set_btn_hovered({})}
                                onClick={() => {
                                    window.open(`http://app.${funblocks_domain}/#/invitation-event`)
                                }}
                            >
                                <span>🎁&nbsp;</span>
                                {intl.formatMessage({ id: 'invite_friends' })}
                            </div>
                        </div>
                    </>
                }
            </div>
            <Box
                component="main"
                sx={{ flexGrow: 1, height: '100%', width: '100%', padding: 0, overflowY: 'auto', paddingLeft: '15px' }}
            >
                {/* <div style={{ paddingLeft: 10, paddingRight: 10 }}> */}
                {currentPage.page}
                {/* </div> */}
            </Box>
        </div>
    );
}

export default Board;
