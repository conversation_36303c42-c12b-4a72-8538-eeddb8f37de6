import { useIntl } from 'react-intl'
import { Selector } from '@/common/selector'
import { useSettings } from '@/common/store/settings'
import { Switch } from '@mui/material'
import {
  is_widget_switched,
  switch_widget,
} from '@/content/container/slidebar-menu/quick-settings'
import { action_bar_widget_id } from '@/content/container/store/view'
import DisabledPages from './disabled_pages'
import { ContextualToolbarSettings } from '@/common/contextual-toolbar-settings'
import browser from 'webextension-polyfill'

const QuickAction = () => {
  const intl = useIntl()
  const { settings = {}, setSettings } = useSettings()
  const contextual_toolbar_img_path = browser.runtime.getURL(
    'dist/assets/contextual_toolbar.png'
  )
  // console.log('settings...........', settings)

  return (
    <div
      style={{
        width: '100%',
        alignItems: 'center',
        justifyContent: 'flex-start',
        padding: 10,
      }}
    >
      {/* <div style={styles.item}>
        <span style={styles.title}>
          {intl.formatMessage({ id: 'settings_quick_action' })}
        </span>
        <Switch
          size="small"
          checked={is_widget_switched(action_bar_widget_id, settings)}
          onChange={(event) =>
            switch_widget(
              action_bar_widget_id,
              event.target.checked,
              settings,
              setSettings
            )
          }
        />
      </div> */}
      <div
        style={{
          marginTop: 20,
        }}
      ></div>
      <ContextualToolbarSettings />

      {/* <div style={{ ...styles.item, paddingTop: 6 }}>
        <span
          style={{
            fontSize: 15,
            color: '#555',
          }}
        >
          {intl.formatMessage({ id: 'settings_quick_action_desc' })}
        </span>
      </div> */}

      <DisabledPages
        widgetId={action_bar_widget_id}
        widgetLabel={intl.formatMessage({ id: 'settings_quick_action' })}
      />

      {is_widget_switched(action_bar_widget_id, settings) && (
        <>
          <div style={styles.item}>
            <span style={styles.title}>
              {intl.formatMessage({ id: 'balloon_auto_hide' })}
            </span>
            <Switch
              checked={settings.balloon_auto_hide === 'true'}
              onChange={(event) => {
                setSettings({
                  ...settings,
                  balloon_auto_hide: event.target.checked ? 'true' : 'false',
                })
              }}
              name="balloon_auto_hide"
              size="small"
            />
          </div>

          <div style={styles.item}>
            <span style={styles.title}>
              {intl.formatMessage({ id: 'balloon_copy_enabled' })}
            </span>
            <Switch
              checked={settings.balloon_copy_enabled === 'true'}
              onChange={(event) => {
                setSettings({
                  ...settings,
                  balloon_copy_enabled: event.target.checked ? 'true' : 'false',
                })
              }}
              name="balloon_copy_enabled"
              size="small"
            />
          </div>
        </>
      )}

      <div
        style={{
          marginTop: 56,
          padding: 18,
          boxShadow: '0px 4px 6px rgba(0, 0, 0, 0.2)',
          border: '1px solid #e3e3e3',
          borderRadius: 6,
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <span style={styles.title}>
            {intl.formatMessage({ id: 'what_is_contextual_toolbar' })}
          </span>
        </div>
        <div
          style={{
            marginTop: 6,
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            columnGap: 6,
          }}
        >
          <div
            style={{
              fontSize: 14,
              color: '#333',
              display: 'flex',
              flexDirection: 'column',
              rowGap: 8,
              flex: 5,
            }}
          >
            {intl
              .formatMessage({ id: 'contextual_toolbar_intro' })
              .split('\n')
              .map((str) => {
                return <span>{str}</span>
              })}
          </div>
          <div style={{ flex: 4 }}>
            <img
              className="highlighted-image"
              width={360}
              src={contextual_toolbar_img_path}
              draggable={false}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

const styles = {
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    display: 'flex',
    paddingTop: '36px',
    columnGap: '10px',
  },

  title: {
    fontWeight: 500,
    fontSize: 16,
    whiteSpace: 'nowrap',
  },
  input: {
    padding: '3px 12px',
    border: '1px solid #ccc',
    borderRadius: 15,
    outline: 'none',
    minWidth: '400px',
    fontSize: 14,
  },
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
    minWidth: 80,
  },
}

export default QuickAction
