{"name": "FunBlocks_AI_Extension", "version": "2.2.2", "main": "index.js", "devDependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@mozilla/readability": "^0.5.0", "@mui/lab": "^5.0.0-alpha.119", "@mui/material": "^5.14.18", "@popperjs/core": "^2.11.3", "@rajesh896/broprint.js": "^2.1.1", "@styled-icons/bootstrap": "^10.47.0", "@styled-icons/boxicons-regular": "10.23.0", "@styled-icons/boxicons-solid": "^10.47.0", "@styled-icons/entypo": "^10.34.0", "@styled-icons/evaicons-outline": "^10.46.0", "@styled-icons/fluentui-system-filled": "^10.35.0", "@styled-icons/fluentui-system-regular": "^10.46.0", "@styled-icons/foundation": "10.28.0", "@styled-icons/ionicons-outline": "^10.34.0", "@styled-icons/ionicons-sharp": "^10.34.0", "@styled-icons/material": "10.28.0", "@styled-icons/material-outlined": "^10.34.0", "@styled-icons/remix-editor": "^10.33.0", "@types/chrome": "^0.0.212", "@types/lodash-es": "^4.17.6", "@types/marked": "^4.0.8", "@types/rangy": "^0.0.38", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/webextension-polyfill": "^0.10.0", "@webcomponents/webcomponentsjs": "^2.7.0", "ahooks": "^3.7.5", "animate.css": "^4.1.1", "antd": "^5.2.3", "autoprefixer": "^10.4.13", "chokidar": "^3.5.3", "classnames": "^2.3.2", "cross-env": "^7.0.3", "dotenv": "^16.0.3", "emoji-picker-react": "^4.4.8", "esbuild": "^0.17.5", "esbuild-plugin-copy": "^2.1.0", "esbuild-style-plugin": "^1.6.1", "html2canvas": "^1.4.1", "husky": ">=6", "i18next": "^22.4.10", "i18next-browser-languagedetector": "^7.0.1", "jquery": "^3.7.1", "json5": "^2.2.3", "konva": "^9.3.6", "lint-staged": ">=10", "lodash-es": "^4.17.21", "markdown-it": "^13.0.1", "markdown-it-highlightjs": "^4.0.1", "nodemon": "^2.0.20", "openai": "^3.2.1", "patch-package": "^6.5.1", "prettier": "^2.8.7", "rangy": "^1.3.1", "react": "^18.2.0", "react-contenteditable": "^3.3.7", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-i18next": "^12.2.0", "react-image-crop": "^11.0.5", "react-intl": "^5.24.4", "react-konva": "^18.2.10", "react-loader-spinner": "^5.4.5", "react-redux": "^7.2.6", "react-share": "4.4.1", "redux": "^4.1.2", "redux-form": "^8.1.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.2.0", "styled-components": "5.3.3", "swr": "^2.0.4", "tailwindcss": "^3.2.7", "tslib": "^2.5.0", "tsup": "^6.7.0", "turndown": "^7.2.0", "typescript": "^4.9.5", "unstated-next": "^1.1.0", "uuid": "^8.0.0", "webextension-polyfill": "^0.10.0", "@chakra-ui/react": "^3.19.1", "@xyflow/react": "^12.6.4", "immutability-helper": "^3.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1"}, "scripts": {"dev": "cross-env NODE_ENV=development NODE_OPTIONS=--max-old-space-size=8192 tsup --watch", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 tsup", "postinstall": "patch-package", "prepare": "husky install"}, "lint-staged": {"*.{js,css,md,ts,tsx}": "prettier --write"}, "dependencies": {"@types/uuid": "^9.0.2"}}